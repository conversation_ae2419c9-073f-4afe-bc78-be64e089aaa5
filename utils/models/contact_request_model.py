from pydantic import HttpUrl

from utils.models.pydantic_utils import PascalModel


class ContactAddress(PascalModel):
    rue: str
    numero: str
    commune: str
    code_postal: str


class ContactFile(PascalModel):
    document_name: str
    file_url: HttpUrl
    extension: str


class ContactData(PascalModel):
    remarque: str
    nom: str
    prenom: str
    adresse: ContactAddress
    phone: str
    email: str
    preference: str
    destinataire: str
    section: str
    sous_section: str
    reference: int | str
    description: str


class ContactRequest(PascalModel):
    client_email: str
    data: ContactData
    files: list[ContactFile] = []
