import os
import re
from urllib.parse import urlparse

from utils.aws_utils import get_resource_key, load_s3_file
from utils.dict_utils import get
from utils.errors import BadRequestError, ForbiddenError
from utils.log_utils import log_err, log_info


def check(bool_, err):
    if not bool_:
        raise err


def is_valid_email(email: str, check_user: bool = False) -> bool:
    """
    @param email : The email address to be validated as a string.
    @param check_user : A boolean value indicating whether to check the availability of the email address by actually attempting to email it.
    Default value is False.
    @return : A boolean value indicating whether the email address is valid.
    Returns True if the email address is valid, False otherwise.
    """
    from validate_email import validate_email_or_fail
    from validate_email.exceptions import (
        DomainBlacklistedError,
        SMTPTemporaryError,
        EmailValidationError,
        SMTPCommunicationError,
    )

    if not isinstance(email, str):
        return False

    is_production = os.environ.get("STAGE", "dev") == "production"
    validate_email_config = {
        "email_address": email,
        "check_format": True,
        "check_blacklist": is_production,
        "check_dns": True,
        "dns_timeout": 1,
        "check_smtp": check_user and is_production,
        "smtp_timeout": 1,
        "smtp_helo_host": "news.resa.be",
        "smtp_from_address": "<EMAIL>",
    }

    try:
        return validate_email_or_fail(**validate_email_config)
    except DomainBlacklistedError as exc:
        raise BadRequestError("Les emails jetables ne sont pas autorisés", error_code="BAD_EMAIL") from exc
    except (SMTPTemporaryError, SMTPCommunicationError) as error:
        log_info(f"Validation for {email!r} is ambiguous: {error}")
        return True  # if ambiguous, better to say the mail is valid than to block incorrectly
    except EmailValidationError as error:
        # Don't block mail if blocked by security policy
        security_errors = [
            "5.7.1",
            "571",
            "5.5.4",
            "554",
            "550 SPF match mandatory",
            "not allowed to send mail from",
        ]
        for security_error in security_errors:
            if security_error in str(error):
                return True
        log_info(f"Validation for {email!r} failed: {error}")
        return False


def minLength(str_, length=3):
    return len(str_) >= length


def isPhone(phone):
    if not phone:
        return False
    phonePattern = re.compile("^((!?[+][0-9]{1,4})||[0])+[0-9]{9}$")
    return phonePattern.match(phone.replace(" ", "").replace("-", "").replace(".", "").replace("/", ""))


def isTestPhone(phone):
    try:
        test_phones = load_s3_file(os.environ["BUCKET"], get_resource_key("test_phones.txt")).split("\n")
        return phone in test_phones
    except Exception as e:
        log_err("Error while checking test phones : " + str(e))
        return False


def isCountryCode(countryCode):
    return len(countryCode) == 2


def is_number(s):
    try:
        float(s)
        return True
    except ValueError:
        return False


def validateAdresse(adresse, check_localite=True):
    if "Rue" not in adresse:
        raise BadRequestError("No Rue in adresse")
    if not isinstance(adresse["Rue"], str):
        raise BadRequestError("adresse Rue must be a string")
    if "NumRue" not in adresse:
        raise BadRequestError("No NumRue in adresse")
    if not isinstance(adresse["NumRue"], str):
        raise BadRequestError("adresse NumRue must be a string")
    if check_localite:
        if "Localite" not in adresse:
            raise BadRequestError("No Localite in adresse")
        if not isinstance(adresse["Localite"], str):
            raise BadRequestError("adresse Localite must be a string")
    if "Cdpostal" not in adresse:
        raise BadRequestError("No Cdpostal in adresse")
    if not isinstance(adresse["Cdpostal"], str):
        raise BadRequestError("adresse Cdpostal must be a string")
    cdpostalPattern = re.compile("^[0-9]{4}$")
    if not cdpostalPattern.match(adresse["Cdpostal"]):
        raise BadRequestError("adresse Cdpostal format must match `^[0-9]{4}$`")
    countryCode = get(adresse, "CodePays", "BE", default_on_empty=True)
    if not isinstance(countryCode, str) or not isCountryCode(countryCode):
        raise BadRequestError("`CodePays` is not a country code")
    return {
        "Cdpostal": adresse["Cdpostal"],
        "Localite": get(adresse, "Localite"),
        "Rue": adresse["Rue"],
        "NumRue": adresse["NumRue"],
        "CodePays": countryCode.upper(),
    }


def cast_bool(value):
    if str(value).lower() == "true":
        return True
    if str(value).lower() == "false":
        return False
    if str(value).lower() == "none":
        return None
    return value


def validate_type_bool(param_value, err="Valeur du paramètre invalid pour type booléen"):
    if param_value not in ["true", "false", True, False]:
        raise BadRequestError(err)
    else:
        return True if param_value in ["true", True] else False


def validate_type_int(param_value, param_name="Le paramètre fourni"):
    try:
        return int(param_value)
    except Exception:
        raise BadRequestError(param_name + " n'est pas un entier")


def validate_type_float(param_value, param_name="Le paramètre fourni"):
    try:
        return float(param_value)
    except Exception:
        raise BadRequestError(param_name + " n'est pas un nombre")


def validate(arg, f, err):
    if f(arg):
        return arg
    else:
        raise err


def check_password_policy(password):
    passwordPattern = re.compile("^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-=]).{8,}$")
    return passwordPattern.match(password)


def alnum_only(txt):
    return "".join([character for character in txt if character.isalnum()])


def check_callback(url):
    if url and os.environ.get("STAGE_TAG") in ("QLA", "PRD"):
        authorized_domains = ["myresa.be", "resa.be"]
        domain_part = urlparse(url).netloc.split(".")
        for authorized_domain in authorized_domains:
            authorized_domain_part = authorized_domain.split(".")
            if ".".join(domain_part[-len(authorized_domain_part) :]) == authorized_domain:
                return True
        raise ForbiddenError("Callback invalide")


def must_be_true(v: bool, field_name: str | None = None, reason: str | None = None) -> bool:
    """
    Assert that the given value is True.

    Parameters
    ----------
    v : bool
        The boolean value to check
    field_name : str | None
        The name of the validated field, used in error messages.
    reason : str | None
        The reason why the field  should be true, used in error messages.

    Returns
    -------
    bool
        The input value if it is True

    Raises
    ------
    ValueError
        If the input value is not True

    """
    if v is not True:
        message = ""
        if field_name:
            message += f"'{field_name}' "
        message += "must be true"
        if reason:
            message += f" ({reason})"
        raise ValueError(message)
    return v
