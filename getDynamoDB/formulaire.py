import json
import os
from copy import deepcopy
from datetime import datetime
from json import <PERSON><PERSON><PERSON><PERSON>ode<PERSON><PERSON>r
from os import environ
from typing import Optional

import boto3
from requests import request

from utils.aws_handler_decorator import commune_handler
from utils.aws_utils import (
    get_dynamodb_table,
    get_resource_key,
    invoke_lambda,
    load_s3_file,
    upload_s3_file,
)
from utils.dict_utils import get
from utils.errors import BadRequestError, InternalServerError
from utils.message_utils import send_in_blue_mail
from utils.validation import is_valid_email, isPhone

RACC_EMAIL_AUTRE = "<EMAIL>"
RACC_EMAIL_CHANTIER = "<EMAIL>"
RACC_EMAIL_FORAIN = "<EMAIL>"
RACC_EMAIL_BUILDING = "<EMAIL>"
RACC_EMAIL_BUILDING_GAZ = "<EMAIL>"
COMMUNES_EMAIL = "<EMAIL>"

PARAMETERS_DICTIONARY = {
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT BUILDING": {
        "destination_email": RACC_EMAIL_BUILDING,
        "metier_template_id": 956,
        "client_template_id": {"FR": 691, "DE": 695},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT BUILDING GAZ": {
        "destination_email": RACC_EMAIL_BUILDING_GAZ,
        "metier_template_id": 956,
        "client_template_id": {"FR": 691, "DE": 695},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT AUTRE": {
        "destination_email": RACC_EMAIL_AUTRE,
        "metier_template_id": 956,
        "client_template_id": {"FR": 691, "DE": 695},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT FORAIN": {
        "destination_email": RACC_EMAIL_FORAIN,
        "metier_template_id": 126,
        "client_template_id": {"FR": 135, "DE": 257},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT CHANTIER": {
        "destination_email": RACC_EMAIL_CHANTIER,
        "metier_template_id": 128,
        "client_template_id": {"FR": 134, "DE": 255},
        "str": True,
    },
    "MODIFICATION RACCORDEMENT/DEMANDE RACCORDEMENT BUILDING": {
        "destination_email": RACC_EMAIL_BUILDING,
        "metier_template_id": 716,
        "client_template_id": {"FR": 693, "DE": 698},
        "str": True,
    },
    "MODIFICATION RACCORDEMENT/DEMANDE RACCORDEMENT BUILDING GAZ": {
        "destination_email": RACC_EMAIL_BUILDING_GAZ,
        "metier_template_id": 716,
        "client_template_id": {"FR": 693, "DE": 698},
        "str": True,
    },
    "MODIFICATION RACCORDEMENT/DEMANDE RACCORDEMENT AUTRE": {
        "destination_email": RACC_EMAIL_AUTRE,
        "metier_template_id": 716,
        "client_template_id": {"FR": 693, "DE": 698},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT TECHNIQUE BUS": {
        "destination_email": RACC_EMAIL_AUTRE,
        "metier_template_id": 131,
        "client_template_id": {"FR": 136, "DE": 256},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT TECHNIQUE ARMOIRE": {
        "destination_email": RACC_EMAIL_AUTRE,
        "metier_template_id": 132,
        "client_template_id": {"FR": 137, "DE": 258},
        "str": True,
    },
    "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT TECHNIQUE SPÉCIFIQUE": {
        "destination_email": RACC_EMAIL_AUTRE,
        "metier_template_id": 133,
        "client_template_id": {"FR": 138, "DE": 259},
        "str": True,
    },
    "MYRESA COMMUNES/AJOUTER UN POINT DE CONSOMMATION": {
        "destination_email": COMMUNES_EMAIL,
        "metier_template_id": 335,
        "client_template_id": {"FR": 336, "DE": 339},
    },
    "MYRESA COMMUNES/SUPPRIMER UN POINT DE CONSOMMATION": {
        "destination_email": COMMUNES_EMAIL,
        "metier_template_id": 335,
        "client_template_id": {"FR": 336, "DE": 339},
    },
    "AUTRE": {"metier_template_id": 802, "client_template_id": {"FR": 319, "DE": 316}},
}


def formulaire_demande(event, config):
    query_params = get(event, "queryStringParameters", {})

    send_type = query_params.get("Type", "MAIL")
    if send_type not in ("MAIL", "SF", "DB"):
        raise BadRequestError("Invalid type, should be MAIL or SF or DB", error_code="BAD_TYPE")
    event_parameters = get_parameters_from_event(event)

    metier_template_id = event_parameters.get("metier_template_id")
    client_template_id = event_parameters.get("client_template_id", {}).get(environ["LANG"])
    body = json.loads(get(event, "body", "{}"))
    phone = get(body, "Phone")
    client_email = get(body, "Email", err=BadRequestError("Email is mandatory"), err_on_empty=True)
    destination_email = event_parameters.get("destination_email")
    sandbox = get(query_params, "Sandbox", "false", default_on_empty=True) == "true"
    files = get(body, "Files", [], default_on_empty=True)
    if files:
        verify_file_format(files)
    load_data(body)

    # Outside of production, change email to yopmail
    if environ.get("STAGE", "dev") != "production":
        if destination_email:
            destination_email = get_yopmail(destination_email)
        client_email = get_yopmail(client_email)

    if not is_valid_email(client_email):
        raise BadRequestError("Email has not an email format", error_code="BAD_EMAIL")
    if phone and not isPhone(phone):
        raise BadRequestError("Phone has not a phone format", error_code="BAD_PHONE")

    if not isinstance(files, list):
        raise BadRequestError(
            "Files should be a list of file dicts with FileData/FileUrl, DocumentName and Extension as keys",
            error_code="BAD_FILE",
        )

    if send_type == "MAIL":
        if not destination_email:
            raise BadRequestError(
                'Field "Destinataire" is mandatory for "type" MAIL.',
                error_code="MISSING_FIELD",
            )
        files_for_metier = files
        if event_parameters.get("str"):
            files_for_metier = str_mail_metier_actions(body, files_for_metier)
        send_sib_form_mail(destination_email, metier_template_id, body, files_for_metier, sandbox)
    elif send_type == "SF":
        send_to_salesforce(client_email, body, files)
    elif send_type == "DB":
        save_to_dynamo(body, files)

    if send_type != "SF" and client_template_id:
        if event_parameters.get("str"):
            str_mail_client_actions(
                client_email,
                client_template_id,
                body,
                files,
                sandbox,
                reply_to=destination_email,
            )
        else:
            send_sib_form_mail(
                client_email,
                client_template_id,
                body,
                files,
                sandbox,
                reply_to=destination_email,
            )

    return {
        "isBase64Encoded": False,
        "statusCode": 201,
        "headers": {"Content-Type": "application/json"},
    }


def verify_file_format(files):
    keys = ["DocumentName", "Extension"]
    for file in files:
        file_keys = file.keys()
        if "FileData" not in file_keys and "FileUrl" not in file_keys:
            raise BadRequestError(
                f"File {file} is missing both 'FileData' and 'FileUrl'",
                error_code="BAD_FILE",
            )
        for key in keys:
            if key not in file_keys:
                raise BadRequestError(f"File {file} is missing {key}", error_code="BAD_FILE")


def str_mail_client_actions(
    client_email,
    client_template_id,
    body,
    files,
    sandbox,
    reply_to: Optional[str] = None,
):
    sent_mails = set()
    send_sib_form_mail(client_email, client_template_id, body, files, sandbox, reply_to)
    sent_mails.add(client_email)

    for partenaire in get(body["Data"], "Partenaire", [], default_on_empty=True):
        if partenaire.get("TypePartenaire") in ("DEMANDEUR", "CONTACT"):
            mail = partenaire.get("Email")

            # Outside of production, change email to yopmail
            if environ.get("STAGE", "dev") != "production":
                mail = get_yopmail(mail)

            if mail not in sent_mails:
                sent_mails.add(mail)
                send_sib_form_mail(mail, client_template_id, body, files, sandbox, reply_to)


def str_mail_metier_actions(body, files):
    # For STR, generate à PDF summary
    pdf_data = generate_str_summary_prd(body)
    return [
        *files,
        {
            "FileData": pdf_data,
            "DocumentName": "Fiche_récapitulative",
            "Extension": "pdf",
        },
    ]


def send_sib_form_mail(
    destination_email: str,
    template_id: int,
    email_parameters: dict,
    files: Optional[list],
    sandbox: bool,
    reply_to: Optional[str] = None,
):
    send_in_blue_mail(
        template_id,
        email_parameters,
        destination_email,
        reply_to,
        "_",
        sandbox,
        attachments=[
            {
                "url": attachment.get("FileUrl"),
                "content": attachment.get("FileData"),
                "name": f"{attachment['DocumentName']}.{attachment['Extension']}",
            }
            for attachment in files
        ],
    )


def dict_to_html(d, level=2):
    # I truly hope you won't have to go through this code. If you really need to, then may the odds be ever in your favor!
    html = ""
    header_style = ' style="margin-top:1rem;margin-bottom:0;"'
    content_style = ' style="margin-top:0.7rem;"'
    # Ensure we do not go below h4
    for key, value in d.items():
        level = min(level, 4)
        if isinstance(value, dict):
            # Increment the header level for nested dictionaries
            html += f'<h{level}{header_style}>{key}:</h{level}><div style="margin-left: 1.5rem;">{dict_to_html(value, level + 1)}</div>'
        elif isinstance(value, list):
            # Lists are converted into unordered lists in HTML
            html += f"<h{level}{header_style}>{key}:</h{level}><ul{content_style}>"
            for item in value:
                if isinstance(item, dict):
                    # Recursive call for nested dictionaries with incremented header level
                    html += f"<li>{dict_to_html(item, level + 1)}</li>"
                elif isinstance(item, list):
                    # Recursive call for nested lists with incremented header level
                    html += f"<li>{dict_to_html_list(item, level + 1)}</li>"
                else:
                    # For string or boolean values in the list
                    html += f"<li{content_style}>{item}</li>"
            html += "</ul>"
        else:
            html += f"<div{content_style}><strong>{key}</strong>: {value}</div>"
    return html


def dict_to_html_list(lst, level):
    # Ensure we do not go below h4
    level = min(level, 4)
    list_style = ' style="margin-top:0.2rem;"'
    html = f"<ul{list_style}>"
    for item in lst:
        if isinstance(item, dict):
            # Recursive call for nested dictionaries
            html += f"<li>{dict_to_html(item, level + 1)}</li>"
        elif isinstance(item, list):
            # Recursive call for nested lists
            html += f"<li>{dict_to_html_list(item, level + 1)}</li>"
        else:
            html += f"<li{list_style}>{item}</li>"
    html += "</ul>"
    return html


def load_data(email_parameters):
    if "Description" in email_parameters and isinstance(email_parameters["Description"], str):
        try:
            email_parameters["Data"] = json.loads(email_parameters["Description"])
            if isinstance(email_parameters["Data"], list):
                email_parameters["Description"] = "<br/>".join(f"{item['label']} : {item['value']}" for item in email_parameters["Data"])
        except JSONDecodeError:
            email_parameters["Data"] = None
    else:
        email_parameters["Data"] = None
        email_parameters["Description"] = ""
    if "CustomsData" in email_parameters:
        email_parameters["CustomsHtml"] = dict_to_html(email_parameters["CustomsData"])


def get_parameters_from_event(event: dict) -> dict:
    try:
        body = json.loads(get(event, "body", "{}"))
    except Exception as e:
        raise BadRequestError("Unable to load JSON", error_code="WRONG_JSON_FORMAT") from e

    section = get(body, "Section", err=BadRequestError("Section is mandatory"), err_on_empty=True).upper()
    sous_section = body.get("SousSection", "").upper()

    if section == "MYRESA COMMUNES":
        commune_validation(event)

    parameters = deepcopy(PARAMETERS_DICTIONARY.get(f"{section}{'/' if sous_section else ''}{sous_section}"))

    if not parameters:
        parameters = deepcopy(PARAMETERS_DICTIONARY["AUTRE"])

    if not parameters.get("destination_email") and body.get("Destinataire"):
        parameters["destination_email"] = body.get("Destinataire")

    return parameters


def generate_str_summary_prd(parameters: dict) -> bytes:
    from jinja2 import Template

    parameters["date"] = datetime.now().strftime("%d/%m/%Y")
    html_summary = Template(load_s3_file(environ["BUCKET"], get_resource_key("templates/STR-summary.html"))).render({"params": parameters})
    lambda_response = invoke_lambda(
        FunctionName=os.environ["HTML_TO_PDF_LAMBDA"],
        InvocationType="RequestResponse",
        Payload=json.dumps({"body": html_summary}),
    )

    # Lire le payload pour obtenir le contenu en tant que bytes
    response_dict = json.loads(lambda_response["Payload"].read())
    pdf_summary = response_dict["results"]

    return pdf_summary


def get_yopmail(mail: str) -> str:
    if not mail:
        return mail

    mail_part = mail.split("@")

    if len(mail_part) != 2 or mail_part[1] == "yopmail.com":
        return mail

    return f"{mail_part[0][:25]}@yopmail.com"


@commune_handler(roles=["POINT_CONSO"])
def commune_validation(event: dict, logged_user) -> str:
    # This function is only there to reuse the commune_handler logic and validations
    pass


def send_to_salesforce(client_email: str, email_parameters: dict, files: Optional[list]):
    sqs = boto3.client("sqs")
    queue_url = os.environ["SALESFORCE_QUEUE_URL"]

    sqs.send_message(
        QueueUrl=queue_url,
        MessageBody=json.dumps({"client_email": client_email, "body": email_parameters, "files": files}),
    )


def save_to_dynamo(data: dict, files: Optional[list]):
    table = get_dynamodb_table(os.environ["FormulairesTable"])

    data["DateCreation"] = datetime.now().isoformat()
    data["Fichiers"] = []

    for file in files:
        try:
            file_name = f"WS79/{file['FileUrl'].split('?')[0].split('/')[-1]}"
            file_data = request("GET", file["FileUrl"]).content
            upload_s3_file(os.environ["BUCKET"], file_name, file_data)
            data["Fichiers"].append(f"s3://{os.environ['BUCKET']}/{file_name}")
        except Exception as e:
            raise InternalServerError("Échec sur le fichier", error_code="FILE_ERROR") from e

    # Insert in dynamo
    table.put_item(Item=data)
