import base64
import json
from os import environ
from typing import Optional

from requests import request
from simple_salesforce import Salesforce, SalesforceMalformedRequest

from utils.aws_handler_decorator import aws_lambda_handler
from utils.aws_utils import get_resource_key, get_secret, load_s3_file
from utils.dict_utils import first, get


@aws_lambda_handler
def handler(event, context):
    for record in event.get("Records", []):
        payload = json.loads(record["body"])
        send_to_salesforce(
            payload.get("client_email", ""),
            payload.get("body", {}),
            payload.get("files", []),
        )


def send_to_salesforce(destination_email: str, email_parameters: dict, files: Optional[list]):
    sf_cred = get_secret(environ["SALESFORCE_API"])
    mapping_file = json.loads(load_s3_file(environ["BUCKET"], get_resource_key("mapping_ws79_salesforce.json")))

    section = get(email_parameters, "Section", "default", default_on_empty=True)
    sous_section = get(email_parameters, "SousSection", "", default_on_empty=True)

    mapping = mapping_file.get(section.lower())
    if mapping:
        if "SpecialType" in mapping:
            special_type = mapping["SpecialType"].get(sous_section.lower())
            if special_type:
                mapping["Type"] = special_type
    else:
        mapping = {"RecordType": section, "Type": sous_section}

    sf = Salesforce(
        username=sf_cred["username"],
        password=sf_cred["password"],
        security_token=sf_cred["security_token"],
        client_id="MyResaAPI",
        consumer_key=sf_cred["client_id"],
        consumer_secret=sf_cred["client_secret"],
        version=sf_cred["api_version"],
        domain="login" if environ["STAGE"] == "production" else "test",
    )

    address = email_parameters.get("Adresse", {})

    description = ""
    if email_parameters.get("Description"):
        description += email_parameters["Description"]
    if email_parameters.get("Remarque"):
        if description:
            description += "\n"
        description += email_parameters["Remarque"]

    case = {
        "Description": description,
        "Type": mapping["Type"],
        "Subject": email_parameters.get("SousSection"),
        "SuppliedEmail": destination_email,
        "SuppliedName": f"{email_parameters.get('Nom')} {email_parameters.get('Prenom')}",
        "SuppliedPhone": email_parameters.get("Phone"),
        "Origin": "Web",
        "Z_SuppliedAddress__City__s": address.get("Commune"),
        "Z_SuppliedAddress__CountryCode__s": "BE",
        "Z_SuppliedAddress__Street__s": f"{address.get('Rue')} {address.get('Numero')}",
        "Z_SuppliedAddress__PostalCode__s": address.get("CodePostal"),
        "Z_PreferredContactMethod__c": email_parameters.get("Preference"),
        "RecordType": {"Name": mapping["RecordType"]},
    }
    if email_parameters.get("Ean"):
        case["Z_ServicePoint__r"] = {"vlocity_cmt__MarketIdentifier__c": email_parameters.get("Ean")}
    if email_parameters.get("CustomsHtml"):
        case["Z_AdditionalInformation__c"] = email_parameters.get("CustomsHtml")

    try:
        case = sf.Case.create(case)
    except SalesforceMalformedRequest as sf_ex:
        ex_content = first(sf_ex.content)
        if ex_content and "vlocity_cmt__ServicePoint__c" in ex_content.get("message") and ex_content.get("errorCode") == "INVALID_FIELD":
            case.pop("Z_ServicePoint__r")
            case["Z_AdditionalInformation__c"] = ""
            if email_parameters.get("Ean"):
                case["Z_AdditionalInformation__c"] += f'<div style="margin-top:0.7rem;"><strong>EAN</strong>: {email_parameters["Ean"]}</div>'
            if email_parameters.get("CustomsHtml"):
                case["Z_AdditionalInformation__c"] += email_parameters["CustomsHtml"]
            case = sf.Case.create(case)
        else:
            raise

    for file in files:
        response = request("GET", file.get("FileUrl"))
        b64_file = base64.b64encode(response.content).decode()

        content_version = sf.ContentVersion.create(
            {
                "title": file.get("DocumentName"),
                "PathOnClient": file.get("DocumentName"),
                "VersionData": b64_file,
            },
        )

        content_version_details = sf.ContentVersion.get(content_version.get("id"))

        sf.contentDocumentLink.create(
            {
                "ContentDocumentId": content_version_details.get("ContentDocumentId"),
                "LinkedEntityId": case.get("id"),
                "ShareType": "I",
                "Visibility": "AllUsers",
            },
        )
