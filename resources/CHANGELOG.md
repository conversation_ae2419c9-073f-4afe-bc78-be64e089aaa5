# MyResaAPI ChangeLog

## 5.13.0-RC.2 / 2025-06-26

### ADDED

- WS201 : Endpoint to send ongoing connection request data to DynamoDB and email a resume URL with the generated DraftID. ([PDDD-15](https://ticketing.resa.be/browse/PDDD-15))
- WS202 : Endpoint to get ongoing connection request data from DynamoDB ([PDDD-16](https://ticketing.resa.be/browse/PDDD-16))
- WS204 : Endpoint to retrieve total power of an installation ([PRORACC-133](https://ticketing.resa.be/browse/PRORACC-133), [PDDD-42](https://ticketing.resa.be/browse/PDDD-42))

### MODIFIED

- WS196 : Now return Energy type ([PDDD-38](https://ticketing.resa.be/browse/PDDD-38))
- WS199 : Restructure and add support for new and modification of connection ([PDDD-17](https://ticketing.resa.be/browse/PDDD-17))
- WS192 : Now accept and return PDF when using appropriate headers ([PDDD-14](https://ticketing.resa.be/browse/PDDD-14))
- WS199 : Use S20 instead of R02 for WorkType MODI_SMART ([PDDD-26](https://ticketing.resa.be/browse/PDDD-26))
- WS199 : Send client mail if WorkType is MODI_SMART ([PDDD-21](https://ticketing.resa.be/browse/PDDD-21))
- WS200 : Add phase and rate information ([PDDD-80](https://ticketing.resa.be/browse/PDDD-80))
- Template changed for new & modi RACC ([PDDD-97](https://ticketing.resa.be/browse/PDDD-97))
- WS200 : Use all user EAN only if no EAN or MeterId provided in params ([PRORACC-209](https://ticketing.resa.be/browse/PRORACC-209))
- WS79 : Request for meter removal now doesn't return the summary pdf to the client ([PRORACC-225](https://ticketing.resa.be/browse/PRORACC-225))
- WS199 : New work type and new behavior for Synergrid connection
  request ([PRORACC-104](https://ticketing.resa.be/browse/PRORACC-104), [PRORACC-114](https://ticketing.resa.be/browse/PRORACC-114))
- WS79: Update template IDs ([PRORACC-202](https://ticketing.resa.be/browse/PRORACC-202))
- WS199: Update email notes format and add missing value for WS79 ([PRORACC-202](https://ticketing.resa.be/browse/PRORACC-202))
- WS201 : Now save files ([PRORACC-74](https://ticketing.resa.be/browse/PRORACC-74))
- WS199 : Send "locataire" info to SAP in a structured field ([PRORACC-241](https://ticketing.resa.be/browse/PRORACC-241))
- WS192 : Fix price calculation and fetch data from SAP ([PDDD-13](https://ticketing.resa.be/browse/PDDD-13), [PRORACC-124](https://ticketing.resa.be/browse/PRORACC-124))
- WS63 : Fix price calculation and fetch data from
  SAP ([PDDD-13](https://ticketing.resa.be/browse/PDDD-13), [PDDD-14](https://ticketing.resa.be/browse/PDDD-14), [PRORACC-124](https://ticketing.resa.be/browse/PRORACC-124))

## 5.12.2 / 2025-06-05

### FIX

- WS205 : Adjust time slot generation for MONTHLY type and fix localized date handling. ([PROMACONSO-214](https://ticketing.resa.be/browse/PROMACONSO-214))

## 5.12.1 / 2025-05-26

### MODIFIED

- WS205 : Add time slot generation and empty record creation logic for missing consumption data. ([PROMACONSO-210](https://ticketing.resa.be/browse/PROMACONSO-210))

## 5.12.0 / 2025-05-19

### ADDED

- Cron : Add cron everyday to trigger alert conso ([PROMACONSO-48](https://ticketing.resa.be/browse/PROMACONSO-48))
- Cron : Add cron every first of the month to trigger bilan conso ([PROMACONSO-103](https://ticketing.resa.be/browse/PROMACONSO-103))
- WS205 : News ws to Get conso smart data  ([PDDD-53](https://ticketing.resa.be/browse/PDDD-53))
- WS206 : Add a WS to save user energy profile in dynamoDB ([PDDD-63](https://ticketing.resa.be/browse/PROMACONSO-49))
- WS208 : Add a WS to get user energy profile and his consumption ([PROMACONSO-45](https://ticketing.resa.be/browse/PROMACONSO-45))
- WS214 : Add a ws to save preference for alert & bilan ([PROMACONSO-104](https://ticketing.resa.be/browse/PROMACONSO-104))
- WS215 : Add a ws to get preference for alert & bilan (([PROMACONSO-104](https://ticketing.resa.be/browse/PROMACONSO-104)))
- WS217 : Used to get target energy profile ([PROMACONSO-126](https://ticketing.resa.be/browse/PROMACONSO-126))
- WS220 : Get smart meter consumption data for commune account ([PROMACONSO-193](https://ticketing.resa.be/browse/PROMACONSO-193))

### MODIFIED

- WS111 & WS112 : Modified ws to save consent history ([PROMACONSO-113](https://ticketing.resa.be/browse/PROMACONSO-113))
- WS112 : Add special action to sync data with consent value ([PROMACONSO-105](https://ticketing.resa.be/browse/PROMACONSO-105))
- WS14 : Add meters info in the response ([PROMACONSO-150](https://ticketing.resa.be/browse/PROMACONSO-150))
- WS14 : Add support for SmartPortalConsent field ([PROMACONSO-47](https://ticketing.resa.be/browse/PROMACONSO-47))
- WS97 : Add support for SmartPortalConsent field ([PROMACONSO-47](https://ticketing.resa.be/browse/PROMACONSO-47))
- WS130 : Remove `NumComp` from response to group all EAN that have different address complement ([PROMACONSO-187](https://ticketing.resa.be/browse/PROMACONSO-187))

## 5.11.1 / 2025-04-22

### ADDED

- WS219 : Get historique index ([PDDD-22](https://ticketing.resa.be/browse/PDDD-22))

### MODIFIED

- WS17 : Now return 'Ville' to make diff between duplicated street name ([PDDD-100](https://ticketing.resa.be/browse/PDDD-100))
- WS181 : Add `metadata_list` transformation function for processing request metadata.

### FIXED

- All WS : Add default Lambda config to boto3 client initialization for consistent retries and timeouts. So a lambda will no longer be triggered multiple time if a timeout
  happens. ([INC-9946](https://ticketing.resa.be/browse/INC-9946))
- WS59 : Fix issue with data fetching due to id being int instead of
  str. ([PDDD-109](https://ticketing.resa.be/browse/PDDD-109),[INC-9661](https://ticketing.resa.be/browse/INC-9661))

## 5.11.0 / 2025-04-01
### ADDED

- WS210 : Post Mollie status waiting ([PDDD-95](https://ticketing.resa.be/browse/PDDD-95))

### MODIFIED

- WS158 : Now return Forfait ER015 ([PDDD-96](https://ticketing.resa.be/browse/PDDD-96))
- WS172 : Now use Forfait ER015 as the lowest forfait ([PDDD-96](https://ticketing.resa.be/browse/PDDD-96))
- WS79 : Remove the SalesForce forcing on some section ([PDD-79](https://ticketing.resa.be/browse/PDD-79))

## 5.10.1-HF.1 / 2025-05-14

### FIXED

- WS33 : Fix date given for smart meter ([PDDD-113](https://ticketing.resa.be/browse/PDDD-113), [INC-10127](https://ticketing.resa.be/browse/INC-10127))

## 5.10.1 / 2025-04-23

### FIXED

- WS59 : Fix issue with data fetching due to id being int instead of
  str. ([PDDD-109](https://ticketing.resa.be/browse/PDDD-109),[INC-9661](https://ticketing.resa.be/browse/INC-9661))

## 5.10.0 / 2025-03-06

### ADDED

- WS211 : Endpoint to get TAD incident locality ([PDDD-82](https://ticketing.resa.be/browse/PDDD-82))
- WS212 : Endpoint to get TAD call reason ([PDDD-82](https://ticketing.resa.be/browse/PDDD-82))
- WS209 : Add a WS to get cpt num based on ean / PostCode & street ([PDDD-89](https://ticketing.resa.be/browse/PDDD-89))

### MODIFIED

- WS68 : Add support for multiple emails sent when using SAP format
- WS68 : Add templates "COMMANDE_GE" and "REPRISE_GE"
- PDF WS171 : Coût has now more info next to it ([RFS-25100](https://ticketing.resa.be/browse/RFS-25100))

### FIXED

- WS59 : Correction action_infos cannot be None ([Sentry-trace](https://sentry.io/organizations/resa-pd/issues/5712231893/events/0bcb5b2aefe744a19b7f8b58fd5128d3/))
- WS09 : Fix on error 404 ([Sentry-fix](https://resa-pd.sentry.io/issues/5692033730/events/58fd53149c79402cb34ba91dceabd89e/))
- WS59 : Correction action_infos cannot be None ([Sentry-bc6d6639](https://sentry.io/organizations/resa-pd/issues/6066722135/events/bc6d6639dce94d7dba2a9bf3cba82fed/ ))

## 5.9.3 / 2025-02-05

### MODIFIED

- WS68 : Change templates used by AVIS_COUPURE_MAIL
- WS68 : Add templates for RACC_PRET_CILE

## 5.9.2 / 2025-01-29
### FIXED
- WS171 : Remove unused field in PDF

## 5.9.1-HF.1 / 2025-01-20
### MODIFIED

- WS59 : Add synergy info, but always false to fix energy error in web page ([PDDD-27](https://ticketing.resa.be/browse/PDDD-27))

## 5.9.1 / 2025-01-09
### FIXED
- WS148 : Fixes and optimization
- WS171 : Fix PDF issues

## 5.9.0 / 2024-12-09
### FIXED
- WS35 : Fix validation of address

### MODIFIED
- WS192 : Add cache
- WS14 : only list active contract and meter info on ean root ([PDDD-47](https://ticketing.resa.be/browse/PDDD-47))
- WS29 : round power to one decimal ([PDDD-47](https://ticketing.resa.be/browse/PDDD-47))
- WS138 : Now really patches the deletion of roles ([PDD-669](https://ticketing.resa.be/browse/PDD-669))
- WS171 : New logics in pdf generation ([PDDD-46](https://ticketing.resa.be/browse/PDDD-46))
- Lambda MonthlyApiActivityChecker : Added information in the email ([PDD-558](https://ticketing.resa.be/browse/PDD-558))

### DELETED
- WS193 : Deleted cause project was abandoned and it caused error in sentry ([PDD-549](https://ticketing.resa.be/browse/PDD-549))

## 5.8.0 / 2024-11-13

### ADDED

- WS199 : Endpoint to submit a connection request ([PDD-674](https://ticketing.resa.be/browse/PDD-674))
- WS200 : Endpoint to get meter information by ean and by address ([PDD-673](https://ticketing.resa.be/browse/PDD-673))

### MODIFIED

- WS17 : Add cache ([PDD-675](https://ticketing.resa.be/browse/PDD-675))
- WS106 : Add cache ([PDD-675](https://ticketing.resa.be/browse/PDD-675))
- WS199 : Use S20 instead of R02 for WorkType MODI_SMART ([PDDD-26](https://ticketing.resa.be/browse/PDDD-26))
- WS199 : Send client mail if WorkType is MODI_SMART ([PDDD-21](https://ticketing.resa.be/browse/PDDD-21))

## 5.7.1 / 2024-11-06

### MODIFIED

- WS172 : Add the rates name ([PDDD-5](https://ticketing.resa.be/browse/PDDD-5))
- WS172 : Convert amperes when more than 1 phase ([PDDD-5](https://ticketing.resa.be/browse/PDDD-5))
- WS29 : Add NbPhasesLibelle field mapped based on NbPhases ([PDDD-18](https://ticketing.resa.be/browse/PDDD-18))
- WS192 : Add price and 3 phase configs
- WS147 : Add change_four in query strings params ([PDDD-11](https://ticketing.resa.be/browse/PDDD-11))
- WS147 : Fix last index value and formating ([PDDD-19](https://ticketing.resa.be/browse/PDDD-19))
- WS147 : Set all cell as text ([PDDD-19](https://ticketing.resa.be/browse/PDDD-19))

### FIXED

- WS155 : Update power charge logic to use standard charge power key
- WS155 : Update to use 'Battery_Capacity_Useable' instead of 'Full'

## 5.7.0 / 2024-10-14

### ADDED

- WS197 : Get EAN register information ([PDD-606](https://ticketing.resa.be/browse/PDD-606))
- WS189 : Get variables from a given brevo template ([PDD-489](https://ticketing.resa.be/browse/PDD-489))
- WS198 : Add a new WS to wrap another Online SHP Api call (The one to get the metadata) ([PDD-608](https://ticketing.resa.be/browse/PDD-608))

### MODIFIED

- WS147 : Adaptation and fixes ([PDD-373](https://ticketing.resa.be/browse/PDD-373))
- WS148 : Adaptation and fixes ([PDD-373](https://ticketing.resa.be/browse/PDD-373))
- WS68 : Now deliver better StatusCode ([PDD-577](https://ticketing.resa.be/browse/PDD-577))
- WS181 : Now accept base64 file in his body ([PDD-656](https://ticketing.resa.be/browse/PDD-656))
- WS68 : Now handle file URL in body ([PDD-672](https://ticketing.resa.be/browse/PDD-672))
- WS68 : Handle all valise kind for SHP storage ([PDDD-10](https://ticketing.resa.be/browse/PDDD-10))
- WS68 : Handle multi email for "EMAIL" "CCI" and add "CC" ([PDD-681](https://ticketing.resa.be/browse/PDD-681))

## 5.6.3 / 2024-09-18

### FIXED

- WS39 : Fix access denied for commune accounts ([PDDD-8](https://ticketing.resa.be/browse/PDDD-8))
- WS56 : Fix access denied for commune accounts ([PDDD-8](https://ticketing.resa.be/browse/PDDD-8))
- WS109 : Fix access denied for commune accounts ([PDDD-8](https://ticketing.resa.be/browse/PDDD-8))

## 5.6.2 / 2024-08-12

### MODIFIED

- WS68 : Handle sender domain error as valid

## 5.6.1 / 2024-08-07

### MODIFIED

- WS68 : Handle certain kind of SMTP error as valid

## 5.6.0 / 2024-08-05

### MODIFIED

- WS110 : Add information in the table MollieStatus ([PDD-298](https://ticketing.resa.be/browse/PDD-298))
- WS29 : Data added for non auth user ([PDD-535](https://ticketing.resa.be/browse/PDD-535))
- WS68 : Add MX and SMTP check on target mail ([PDD-596](https://ticketing.resa.be/browse/PDD-596))

### ADDED

- WS187 : Retrieve the information from mollie in the table MollieStatus ([PDD-298](https://ticketing.resa.be/browse/PDD-298))
- WS196 : Is EAN owned by Resa ([PDD-544](https://ticketing.resa.be/browse/PDD-544))

## 5.5.0 / 2024-06-27

### MODIFIED

- WS181 : Add wrapping for online SHP upload document ([PDD-580](https://ticketing.resa.be/browse/PDD-580))

## 5.4.0 / 2024-05-30

### MODFIED

- WS69 : Now verifiy if phone number isn't in a blocked list ([PDD-541](https://ticketing.resa.be/browse/PDD-541))

## 5.3.0 / 2024-05-13

### ADDED

- WS195 : Generate a UUID if 'num' is not present in the query string; otherwise, generate a list containing 'num' * UUIDs. ([PDD-534](https://ticketing.resa.be/browse/PDD-534))
- simpleCompute Lambda : new lambda as been created ([PDD-534](https://ticketing.resa.be/browse/PDD-534))
- Lambda Api Checker : Check every api last call and send a recap every month ([PDD-429](https://ticketing.resa.be/browse/PDD-429))

### MODIFIED

- Commune index :  Modification of municipal indexes following feedback from Liège entity ([PDD-373](https://ticketing.resa.be/browse/PDD-373))
- WS79 : check files attributes ([PDD-522](https://ticketing.resa.be/browse/PDD-522))
- WS34 : Fetch index based on contract linked to connected user BP instead of last contract name ([PDD-343](https://ticketing.resa.be/browse/PDD-343))
- WS14 : Remove the "only active EAN" restriction ([PDD-343](https://ticketing.resa.be/browse/PDD-343))
- WS79 : Handle extra fields when sending to salesforce ([PDD-545](https://ticketing.resa.be/browse/PDD-545))

### FIX

- WS68 : Fix sms templates name saved in SharePoint ([MYRESA-API-J6](https://resa-pd.sentry.io/issues/5209884271/))

## 5.2.1 / 2024-05-06

### MODIFIED

- WS79 : Update mails for racc forms + force sending to set mail ([PDD-548](https://ticketing.resa.be/browse/PDD-548))

## 5.2.0 / 2024-04-29

### ADDED

- WS193 : Callback for job completion with twilio, upload twilio data on our
  S3 ([PDD-502](https://ticketing.resa.be/browse/PDD-502))
- Lambda TwilioCallsDataJob : Fire every month and retrieve data from twilio and call WS193 for each
  days ([PDD-502](https://ticketing.resa.be/browse/PDD-502))
- Lambda TwilioS3ToExcelAndMail : Fire every month (after twilioCallsDatajob) to retrieve data from our s3 and send the
  info into an excel by mail ([PDD-502](https://ticketing.resa.be/browse/PDD-502))

### MODIFIED

- Restore borne recharge new format
- WS112 : remove mandatory auth

## 5.1.2 / 2024-04-15

### MODIFIED

WS68 : Add urllib "unquote" to replace %20 in valise url ([MYRESA-API-H1](https://resa-pd.sentry.io/issues/5144950406/))

## 5.1.1 / 2024-04-08

### MODIFIED

- Revert borne recharge new format

## 5.1.0 / 2024-04-03

### ADDED

- WS191 : Get EAN lock status on /ean/:Ean/lockStatus ([PDD-500](https://ticketing.resa.be/browse/PDD-500))
- WS188 : retourne l'identifiant MyRE depuis un id HGZ ([PDD-463](https://ticketing.resa.be/browse/PDD-463))
- WS192 : Endpoint to fetch possible connection fee  ([PDD-481](https://ticketing.resa.be/browse/PDD-481))
- WS194 : Passthrough to SHP search WS on /sharepoint/passthrough/search ([PDD-445](https://ticketing.resa.be/browse/PDD-445))
- Expose SharePoint WS ([PDD-296](https://ticketing.resa.be/browse/PDD-296))
    - WS177 : GET endpoint for SharePoint valise search - returns JSON information for the specified valise.
    - WS178 : POST endpoint for SharePoint valise creation.
    - WS179 : GET endpoint to list documents in a SharePoint valise.
    - WS180 : GET endpoint to find a document in a SharePoint valise - handles large files by storing in S3.
    - WS181 : PUT endpoint to upload documents in a SharePoint valise.
    - WS182 : PUT endpoint to copy documents in a SharePoint valise.

### MODIFIED

- WS103 : Now go throught a SQL query ([PDD-412](https://ticketing.resa.be/browse/PDD-412))
- WS153 : Now it accept multiple input as a list. ([PDD-288](https://ticketing.resa.be/browse/PDD-288))
- WS153 : Modify models, now take (Libelle and Valeur) + Creation of a script ([PDD-91](https://ticketing.resa.be/browse/PDD-91))
- WS68 : Add templates ACTIVATION_AFF_EAN and DESACTIVATION_AFF_EAN([PDD-432](https://ticketing.resa.be/browse/PDD-432))
- digacertStoreCert : Increase timeout limit for SHP requests ([PDD-509](https://ticketing.resa.be/browse/PDD-509))

### FIX

- WS27 : Now handle empty queryStringParameters ([MYRESA-API-7B](https://resa-pd.sentry.io/issues/4730449637/))
- WS33 : Now handle empty queryStringParameters ([MYRESA-API-7C](https://resa-pd.sentry.io/issues/4730449639/))

## 5.0.4 / 2024-03-07

### ADDED

- WS173 : Get data from "Borne Recharge" ([PDD-289](https://ticketing.resa.be/browse/PDD-289))
- WS174 : Modify data from "Borne Recharge" ([PDD-289](https://ticketing.resa.be/browse/PDD-289))
- WS175 : Delete data from "Borne Recharge" ([PDD-289](https://ticketing.resa.be/browse/PDD-289))
- WS158: Retrieve the rates for non-periodic connections ([PDD-246](https://ticketing.resa.be/browse/PDD-246))
- WS172 : Calculate the cost of a connection modification ([PDD-258](https://ticketing.resa.be/browse/PDD-258))
- WS155 : Expose ev-database on GET /vehicules AND Added a Lambda function that automatically triggers every 24 hours to fetch information from
  EVDB. ([PDD-226](https://ticketing.resa.be/browse/PDD-226))
- Vehicles data management ([PDD-231](https://ticketing.resa.be/browse/PDD-231)):
    - WS157 : Post Add a vehicles ([PDD-228](https://ticketing.resa.be/browse/PDD-228))
    - WS156 : Get Retrieve data of a specified vehicles ([PDD-227](https://ticketing.resa.be/browse/PDD-227))
    - WS159 : Delete Delete a vehicles ([PDD-230](https://ticketing.resa.be/browse/PDD-230))
  - WS171 : download PDF vehicles data ([PDD-242](https://ticketing.resa.be/browse/PDD-242))
  - WS170 : Send mail with vehicles data ([PDD-241](https://ticketing.resa.be/browse/PDD-241))

### MODIFIED

- WS29 : Optional "NumCpt" if the user is authenticated ([DMC-815](https://ticketing.resa.be/browse/DMC-815))
- WS62 : Add mandatory auth and use "Ean" instead of "Buspartner" to validate that the Ean belong to the auth user ([DMC-815](https://ticketing.resa.be/browse/DMC-815))
- WS45 : Add mandatory auth and use "Ean" instead of "ContractAccount" to validate that the Ean belong to the auth user ([DMC-815](https://ticketing.resa.be/browse/DMC-815))
- WS46 : Add mandatory auth and use "Ean" instead of "ContractAccount" to validate that the Ean belong to the auth user ([DMC-815](https://ticketing.resa.be/browse/DMC-815))
- WS56 : Add mandatory auth and use "NumDossier" instead of "Id" to validate that the NumDossier belong to the auth user ([DMC-815](https://ticketing.resa.be/browse/DMC-815))
- WS55 : Validate that the contract id belong to the auth user ([DMC-815](https://ticketing.resa.be/browse/DMC-815))
- WS39 : Force authorization token and check that the dossier is assigned to the user making the call ([DMC-814](https://ticketing.resa.be/browse/DMC-814))
- WS109 : Force authorization token and check that the dossier is assigned to the user making the call ([DMC-814](https://ticketing.resa.be/browse/DMC-814)
- WS79 : Customs part is now more flexible and easy to use ([PDD-374](https://ticketing.resa.be/browse/PDD-374))
- WS79 : Add choice to send form to SalesForce, or by mail, or to save on DB ([PDD-75](https://ticketing.resa.be/browse/PDD-75))
- WS29 : Add PTotProd to fields returned without authentication ([PDD-395](https://ticketing.resa.be/browse/PDD-395))
- WS35 : Add body content validations ([PDD-27](https://ticketing.resa.be/browse/PDD-27), [INC-4366](https://ticketing.resa.be/browse/INC-4366))
- WS106 : Add filter for gaz & elec ([PDD-139](https://ticketing.resa.be/browse/PDD-139), [DME-3704](https://ticketing.resa.be/browse/DME-3704))
- WS114 : Modifications to fetch data from SAP with pricing ([PDD-214](https://ticketing.resa.be/browse/PDD-214), [DME-3721](https://ticketing.resa.be/browse/DME-3721))
- WS68 : Adapt new tarif ([PDD-481](https://ticketing.resa.be/browse/PDD-481), [DME-5082](https://ticketing.resa.be/browse/DME-5082))
- Template mail : Communication après estimation ([PDD-435](https://ticketing.resa.be/browse/PDD-435))
- WS79 : Send SalesForce form to a queue to handle possible SF unavailability ([PDD-436](https://ticketing.resa.be/browse/PDD-436))

### FIXED

- WS117/WS59 : Fix matching on 8 last numbers (instead of 9) for fix phone ([DMC-807](https://ticketing.resa.be/browse/DMC-807))
- WS95 : Fix security issue ([DMC-1690](https://ticketing.resa.be/browse/DMC-1690), [PDD-131](https://ticketing.resa.be/browse/PDD-131))
- EAN is now in str to fix it for propane ([PDD-138](https://ticketing.resa.be/browse/PDD-138)) :
    - WS14
    - WS97
    - WS29
    - WS24
    - Utils/models/user.py
- WS14 : out of range now return "USER_DATA_NOT_FOUND" ([MYRESA-API-5K](https://resa-pd.sentry.io/issues/**********/))
- WS79 : json loads now return "WRONG_JSON_FORMAT" ([MYRESA-API-60](https://resa-pd.sentry.io/issues/4606085964/))
- WS26 : Now handle 404 not found ([MYRESA-API-5X](https://resa-pd.sentry.io/issues/4595298190/))
- Fix endpoints caching time

### DELETED

- WS90 : Delete unused and unsecure WS
- WS91 : Delete unused and unsecure WS
- WS92 : Delete unused and unsecure WS
- WS05 : Delete unused and unsecure WS
- WS07 : Delete unused and unsecure WS
- WS08 : Delete unused and unsecure WS
- WS72 : Deleted WS72 (DELETE utilisateurs/ean/{Ean}) ([PDD-413](https://ticketing.resa.be/browse/PDD-413))

## 4.6.2 / 2024-02-21

- WS68 : Update templates mail for FICA_RECOUVREMENT_ ([PDD-264](https://ticketing.resa.be/browse/PDD-264), [DME-2856](https://ticketing.resa.be/browse/DME-2856))

## 4.6.1-HF.1 / 2024-02-19

- WS68 : remove forced RelyTo

## 4.6.1 / 2024-02-05

### MODIFIED

- WS33 : Now return boolean instead of 'X' or '' for field "CptSmart" ([PDD-437](https://ticketing.resa.be/browse/PDD-437))

### HOTFIX

- WS79 : Changement du RecordType "Relocation" vers "Relocation and Contracts" ([INC-6267](https://ticketing.resa.be/browse/INC-6267))

## 4.6.0 / 2023-12-19

### ADDED

- WS183 : POST /pannes/smart send state of interruptions on the smart meter
  network ([PDD-303](https://ticketing.resa.be/browse/PDD-303), [DME-3512](https://ticketing.resa.be/browse/DME-3512))
- WS184 : GET /pannes/smart get state of interruptions on the smart meter
  network ([PDD-303](https://ticketing.resa.be/browse/PDD-303), [DME-3512](https://ticketing.resa.be/browse/DME-3512))

## 4.5.1 / 2023-12-14

### ADDED

- General : Error monitoring in Sentry
- GET WS147 : Download batch index template for commune ([PDD-198](https://ticketing.resa.be/browse/PDD-198),[DME-3828](https://ticketing.resa.be/browse/DME-3828))
- POST WS148 : Upload batch index for commune ([PDD-199](https://ticketing.resa.be/browse/PDD-199),[DME-3828](https://ticketing.resa.be/browse/DME-3828))

### MODIFIED

- General : Update to python 3.11
- WS03 : Increase mail link validity when resetting account password ([PDD-282](https://ticketing.resa.be/browse/PDD-282), [INC-5301](https://ticketing.resa.be/browse/INC-5301))
- WS04.1 : Increase mail link validity when activating account ([PDD-282](https://ticketing.resa.be/browse/PDD-282), [INC-5301](https://ticketing.resa.be/browse/INC-5301))
- WS04.4 : Increase mail link validity when updating account connexion
  mail ([PDD-282](https://ticketing.resa.be/browse/PDD-282), [INC-5301](https://ticketing.resa.be/browse/INC-5301))
- WS154 : Increase mail link validity when deleting account ([PDD-282](https://ticketing.resa.be/browse/PDD-282), [INC-5301](https://ticketing.resa.be/browse/INC-5301))

### FIX

- WS95 : Fix security issue ([DMC-1690](https://ticketing.resa.be/browse/DMC-1690), [PDD-131](https://ticketing.resa.be/browse/PDD-131))
- WS125 : Fix exception when an empty list is returned ([MYRESA-API-4M](https://resa-pd.sentry.io/issues/**********/))
- WS145 : Fix unlinked flag in SAP
- WS138 : Delete "phone" if the phone is missing or None. ([PDD-371](https://ticketing.resa.be/browse/PDD-371), [MYRESA-API-5K](https://resa-pd.sentry.io/issues/**********/))
- BasicAuthorizer : Fix performance problems with using parallel execution and cache.
    - Before :
        - cold start : ~8s
        - hot start : ~5s
    - After :
        - cold start : ~3.5s
        - hot start : <1s
- WS33 : Set start and end date to the same value in the case of a smart
  counter ([PDD-396](https://ticketing.resa.be/browse/PDD-396), [INC-5729](https://ticketing.resa.be/browse/INC-5729))

## 4.3.0-hf.1 / 2023-12-05

### MODIFIED

- WS25 : Filter by default active interruptions to last year and resolved one to last month ([PDD-417](https://ticketing.resa.be/browse/PDD-417))
- WS125 : Filter by default active interruptions to last year and resolved one to last month ([PDD-417](https://ticketing.resa.be/browse/PDD-417))

## 4.3.0 / 2023-10-16

### MODIFIED

- WS135 : Added "Valide" field to know if a user account is validated or not ([PDD-284](https://ticketing.resa.be/browse/PDD-284))
- WS154 : Send mail for account deletion ([PDD-28](https://ticketing.resa.be/browse/PDD-28), [DME-2263](https://ticketing.resa.be/browse/DME-2263))
- WS84 : Add SAP disconnected flag ([PDD-28](https://ticketing.resa.be/browse/PDD-28), [DME-2263](https://ticketing.resa.be/browse/DME-2263))

## 4.2.1 / 2023-09-27

### FIX

- WS79 : Don't send confirmation email when senting to SalesForce ([PDD-39](https://ticketing.resa.be/browse/PDD-39))

## 4.2.0 / 2023-09-26

### MODIFIED

- WS79 : Add sending form to SalesForce ([PDD-39](https://ticketing.resa.be/browse/PDD-39))

## 4.1.1 / 2023-09-19

### MODIFIED

- WS31 : Modified required parameters for case with index token ([PDD-40](https://ticketing.resa.be/browse/PDD-40), [DME-2344](https://ticketing.resa.be/browse/DME-2344))
- WS68 : Add template SMS for index confirmation with voicebot ([PDD-150](https://ticketing.resa.be/browse/PDD-150), [DME-2344](https://ticketing.resa.be/browse/DME-2344))
- WS68 : Adapt index SMS to use language in url ([PDD-51](https://ticketing.resa.be/browse/PDD-51), [DMC-1076](https://ticketing.resa.be/browse/DMC-1076))
- WS68 : Adapt index SMS to use token in url ([PDD-40](https://ticketing.resa.be/browse/PDD-40), [DME-2344](https://ticketing.resa.be/browse/DME-2344))
- WS68 : Updates text for index sms ([PDD-51](https://ticketing.resa.be/browse/PDD-51), [DMC-1076](https://ticketing.resa.be/browse/DMC-1076))
- WS59 : Add coordinator information ([PDD-106](https://ticketing.resa.be/browse/PDD-106), [DME-2940](https://ticketing.resa.be/browse/DME-2940))

## 4.0.8 / 2023-09-14

### ADDED

- Health check : endpoint to check accessibility on /health ([PDD-132](https://ticketing.resa.be/browse/PDD-132))

### HOTFIX

- WS130 : Fix cryptography and pyOpenSSL version

## 4.0.6 / 2023-09-11

### MODIFIED

- gazMeterPin : Allow 7SAG and 7AMX counter id format ([PDD-243](https://ticketing.resa.be/browse/PDD-243), [RFS-13106](https://ticketing.resa.be/browse/RFS-13106))

## 4.0.5-hotfix.1 / 2023-08-31

### HOTFIX

- WS129 : Fix error when empty 'max_power', 'voltage', and 'amperage' ([PDD-249](https://ticketing.resa.be/browse/PDD-249), [INC-5150](https://ticketing.resa.be/browse/INC-5150))

## 4.0.5 / 2023-07-03

### ADDED

- Add NoName request analyser

### FIXED

- WS59 : Fix error if the dossier is not a R01 or R02 ([PDD-167](https://ticketing.resa.be/browse/PDD-167), [INC-4603](https://ticketing.resa.be/browse/INC-4603))

## 4.0.4

### NO CHANGE FOUND

## 4.0.3 / 2023-06-13

### MODIFIED

- WS25 : Add "EnCours" filter ([PDD-141](https://ticketing.resa.be/browse/PDD-141), [INC-4291](https://ticketing.resa.be/browse/INC-4291))

### FIXED
- WS140 : Fix truncated data on first month ([PDD-141](https://ticketing.resa.be/browse/PDD-141), [INC-4291](https://ticketing.resa.be/browse/INC-4291))
- WS140 : Fix EP led count ([PDD-141](https://ticketing.resa.be/browse/PDD-141), [INC-4291](https://ticketing.resa.be/browse/INC-4291))
- WS24 : Fix error if no value for SQL field "CoupureOuGElectrogene" ([PDD-141](https://ticketing.resa.be/browse/PDD-141), [INC-4291](https://ticketing.resa.be/browse/INC-4291))
- WS34 : Fix comparison with name that contain accented character ([DMC-1723](https://ticketing.resa.be/browse/DMC-1723))

## 4.0.2 / 2023-05-23

### MODIFIED

- WS29 : Add switching hours for smart meter ([DME-1418](https://ticketing.resa.be/browse/DME-1418))
- WS29 : Remove switch hour for meter without switching ([DME-1418](https://ticketing.resa.be/browse/DME-1418))

### FIXED

- WS120 : Fix missing CityCode in
  response ([PDD-36](https://ticketing.resa.be/browse/PDD-36), [DMC-1638](https://ticketing.resa.be/browse/DMC-1638), [INC-3171](https://ticketing.resa.be/browse/INC-3171))
- WS125 : Fix incorrect filters if zipcode
  used ([PDD-97](https://ticketing.resa.be/browse/PDD-97), [DMC-1639](https://ticketing.resa.be/browse/DMC-1639), [INC-3689](https://ticketing.resa.be/browse/INC-3689))
- WS64 : PPP activation error if no phone number on SAP ([DMC-1674](https://ticketing.resa.be/browse/DMC-1674), [INC-3668](https://ticketing.resa.be/browse/INC-3668))

## 4.0.1 / 2023-04-26

### MODIFIED

- WS68 : Add DE templates for ACTIVATION_PORT_P1 / DESACTIVATION_PORT_P1
- WS113 : Create SharePoint "valise" if not
  found ([PDD-99](https://ticketing.resa.be/browse/PDD-99), [DMC-1566](https://ticketing.resa.be/browse/DMC-1566), [INC-3720](https://ticketing.resa.be/browse/INC-3720))

## 4.0.0 / 2023-04-18

### ADDED

- WS03.3 : Endpoint to validate a contact mail on GET
  /utilisateurs/activate/checkMail ([PDD-29](https://ticketing.resa.be/browse/PDD-29), [DME-2106](https://ticketing.resa.be/browse/DME-2106))

### MODIFIED

- WS03 : Add optional query string parameters "ContactEmail" and "ErrorCallback" to support contact mail validation
  flow ([PDD-29](https://ticketing.resa.be/browse/PDD-29), [DME-2106](https://ticketing.resa.be/browse/DME-2106))
- General security : Token generation/validation are now in ES256 instead of HS256
- WS14 : Switch to SAP data ([PDD-61](https://ticketing.resa.be/browse/PDD-61))
- WS14 : Check mail for SmartPortal link ([PDD-58](https://ticketing.resa.be/browse/PDD-58))
- WS121 : prevent linking an account to a BP already linked to another active account
- WS34 : Support inversion nom/prenom ([PDD-30](https://ticketing.resa.be/browse/PDD-30), [DMC-1402](https://ticketing.resa.be/browse/DMC-1402))
- WS34 : Add injection history and "CodeCadran" ([DME-1554](https://ticketing.resa.be/browse/DME-1554))
- WS127 : Update FME URL
- WS03 : Force 2FA mail ([PDD-61](https://ticketing.resa.be/browse/PDD-61))
- WS121 : check valid_phone in user/bp matching ([PDD-61](https://ticketing.resa.be/browse/PDD-61))

### FIXED

- WS68 : Use index to search user by phone number ([PDD-88](https://ticketing.resa.be/browse/PDD-88), [DMC-1551](https://ticketing.resa.be/browse/DMC-1551))
- WS97 : Fix update error if the mail was previously created with upper case letter

## 3.15.2 / 2023-02-23

### FIXED

- WS68 : Fix user search with non lower case email ([PDD-68](https://ticketing.resa.be/browse/PDD-68), [DMC-1495](https://ticketing.resa.be/browse/DMC-1495))

## 3.15.1-hotfix.1 / 2023-01-20

### HOTFIX

- WS03.1 : Fix type allowGhost instead of allow_ghost ([PDD-37](https://ticketing.resa.be/browse/PDD-37))
- WS68 : handle alt domain name (for MyRESA Commune) ([PDD-38](https://ticketing.resa.be/browse/PDD-38))

## 3.15.1 / 2023-01-17

### MODIFIED

- WS68 : Add new templates for index communications ([DME-2359](https://ticketing.resa.be/browse/DME-2359))

## 3.15.0 / 2023-01-12

### ADDED

- WS135 : Endpoint to get all users linked to a commune (commune admin only) on GET /communes/utilisateurs ([DME-1961](https://ticketing.resa.be/browse/DME-1961))
- WS136 : Endpoint to get a user linked to a commune (commune admin only) on GET /communes/utilisateurs/\<Uid\> ([DME-1989](https://ticketing.resa.be/browse/DME-1989))
- WS137 : Endpoint to create a users linked to a commune (commune admin only) on POST /communes/utilisateurs ([DME-1991](https://ticketing.resa.be/browse/DME-1991))
- WS138 : Endpoint to edit a user linked to a commune (commune admin only) on PATCH /communes/utilisateurs/\<Uid\> ([DME-1993](https://ticketing.resa.be/browse/DME-1993))
- WS145 : Endpoint to delete a user linked to a commune (commune admin only) on DELETE /communes/utilisateurs/\<Uid\> ([DME-2007](https://ticketing.resa.be/browse/DME-2007))
- WS142 : Endpoint to get roles that can be used by a commune on GET /communes/roles ([DME-2001](https://ticketing.resa.be/browse/DME-2001))
- WS133 : Endpoint to get dossiers related to a commune on GET /communes/demande_travaux/dossiers ([DME-1957](https://ticketing.resa.be/browse/DME-1957))
- WS134 : Endpoint to get dossier details related to a commune on GET /communes/demande_travaux/dossiers/\<Id\> ([DME-1959](https://ticketing.resa.be/browse/DME-1959))
- WS130 : Endpoint to get EANs related to a commune on GET /communes/ean ([DME-1951](https://ticketing.resa.be/browse/DME-1951))
- WS131 : Endpoint to get EAN detail related to a commune on GET /communes/ean/\<Ean\> ([DME-1953](https://ticketing.resa.be/browse/DME-1953))
- WS132 : Endpoint to get EAN history related to a commune on GET /communes/ean/\<Ean\>/historique ([DME-1955](https://ticketing.resa.be/browse/DME-1955))
- WS140 : Endpoint to get consumption and led info of EP related to a commune on GET /communes/ep ([DME-1997](https://ticketing.resa.be/browse/DME-1997))

### MODIFIED

- WS79 : Add case for MyResa Communes ([DME-2009](https://ticketing.resa.be/browse/DME-2009))
- WS79 : Add "Destinataire" field to force a destination mail
- WS24 : Allow a list of postal code
- WS25 : Allow a list of postal code
- WS125 : Allow a list of postal code
- WS14 : Add "Commune" field to hold new "MyResa Communes" data ([DME-1949](https://ticketing.resa.be/browse/DME-1949))
- WS14bis : Add "Commune" field to hold new "MyResa Communes" data ([DME-1949](https://ticketing.resa.be/browse/DME-1949))
- WS20 : Add "IdCommune" search filter ([DME-1945](https://ticketing.resa.be/browse/DME-1945))

## 3.14.0-hotfix.1 / 2022-12-21

### HOTFIX

- WS129 : Fix Entreprise fields incorrectly set as mandatory

## 3.14.0 / 2022-12-20

### ADDED

- WS127 : Endpoint to trigger FME plan upload job on /raccordement/upload_plan ([DME-1552](https://ticketing.resa.be/browse/DME-1552))

### FIXED
- WS03 : Return coherent error ([INC-2048](https://ticketing.resa.be/browse/INC-2048))
- WS77 : Return same link id same document name to upload, to avoid creating multiple document that will conflict on upload ([DMC-945](https://ticketing.resa.be/browse/DMC-945))
- WS77 : Retry 5 times sharepoint upload ([DMC-945](https://ticketing.resa.be/browse/DMC-945))
- WS24 : Add missing interruption Id ([DMC-1307](https://ticketing.resa.be/browse/DMC-1307))
- WS26 : Remove interruption type limitation ([DMC-1307](https://ticketing.resa.be/browse/DMC-1307))

### MODIFIED

- WS68 : Add CCI field support

## 3.13.0 / 2022-12-01

### ADDED

- WS153 : Endpoint to declare a charging station on POST /bornes_recharge ([DME-2208](https://ticketing.resa.be/browse/DME-2208))

## 3.12.2 / 2022-11-15

- WS68 : Fix user search by phone and add search by bp ([DMC-1199](https://ticketing.resa.be/browse/DMC-1199))

## 3.12.1

### FIXED
- WS68 : Force mail FR when DE not available for unknown users ([DMC-984](https://ticketing.resa.be/browse/DMC-984))

## 3.12.0

### ADDED

- WS03.1 : Endpoint to send SMS validation code on /utilisateurs/activate/sendSms ([DMC-837](https://ticketing.resa.be/browse/DMC-837))
- WS03.2 : Endpoint to verify SMS validation code on /utilisateurs/activate/checkSms ([DMC-837](https://ticketing.resa.be/browse/DMC-837))
- WS03 : Add query params "Callback2FA" to use when 2FA needed ([DMC-837](https://ticketing.resa.be/browse/DMC-837))
- WS152 : Generic passthrough endpoint to https://api.ibanity.com/ on /ibanity ([DMC-907](https://ticketing.resa.be/browse/DMC-907))

### MODIFIED

- WS04 : Invalidate phone number if changed ([DMC-837](https://ticketing.resa.be/browse/DMC-837))
- WS96 : Invalidate phone number if changed ([DMC-837](https://ticketing.resa.be/browse/DMC-837))
- SyncUser : Don't user phone number if not validated ([DMC-837](https://ticketing.resa.be/browse/DMC-837))
- WS03.2 : Only allow one call all 10 seconds to block bruteforce

### FIXED

- WS03 : Fix error 500 if no "Callback2FA"
- WS03 : Fix missing token in "Callback2FA"
- WS03.1/WS03.2 : Check token from query url
- WS03.1 : Add rate limitation
- WS14 : Add "ValidPhone" field
- WS68 : Handle environments in index mails ([DMC-930](https://ticketing.resa.be/browse/DMC-930))
- WS03 : Use different mail template when 2FA needed

## 3.11.4 / 2022-10-10

### MODIFIED

- WS79 : Add "Destinataire" field to force a destination mail

## 3.11.3 / 2022-08-05

### FIXED

- WS29 : Fix exception
- WS117 : Invert and concat lastname and firstname for better matching ([DME-1707](https://ticketing.resa.be/browse/DME-1707))
- WS59 : Invert and concat lastname and firstname for better matching ([DME-1707](https://ticketing.resa.be/browse/DME-1707))

## 3.11.2 / 2022-07-22

### FIXED

- WS29 : Allow unauthenticated call but return only a subset of data ([DMC-950](https://ticketing.resa.be/browse/DMC-950))

## 3.11.1 / 2022-07-07

### FIXED

- WS29 : Force authorization token and check that the EAN is assigned to the user making the call ([DMC-950](https://ticketing.resa.be/browse/DMC-950))
- WS34 : Force authorization token and check that the EAN is assigned to the user making the call ([DMC-950](https://ticketing.resa.be/browse/DMC-950))

## 3.11.0 / 2022-06-24

### MODIFIED

- WS43 : Translate error 404 text ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS122 : Add language support for confirmation-desinscription redirect ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS68 : Use FR if language not available ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- SAP-Passthrough : Force lang in upper ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS43 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS17 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- PassThrough : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS63 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS111 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS14 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS59 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))
- WS09 / WS124 : Add language support and translation ([DME-1460](https://ticketing.resa.be/browse/DME-1460))

### FIXED

- WS113 : Change DIGACERT to ELGACERT in uploaded documents
- WS68 : Add MyResa account mails ([DMC-557](https://ticketing.resa.be/browse/DMC-557))
- Fix aws-psycopg2 version to 1.2.1

## 3.9.2 / 2022-06-13

### MODIFIED

- WS25 : <span style="color:red">REVERT</span> Add filter to remove individual interruptions

### FIXED

- WS68 : Add attachments in SharePoint mail copy
- WS79 : Send summary pdf to STR mails ([DevOps-2547](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/2547/))
- WS72 : Fix timeout error by using BP to fetch user ([DMC-729](https://ticketing.resa.be/browse/DMC-729))

## 3.9.1 / 2022-06-08

### MODIFIED

- WS77 : Add S3 pre-signed URL to metadata on DynamoDB for better tracking in case of exception
- WS25 : Add filter to remove individual interruptions

## 3.9.0 / 2022-06-02

### ADDED

- ws129 : Endpoint to fetch list of charging station /bornes_recharge (partially [DME-1151](https://ticketing.resa.be/browse/DME-1151))

### MODIFIED

- WS68 : Add support for attachment in mails with "DOCUMENT" field ([DME-1175](https://ticketing.resa.be/browse/DME-1175))

### FIXED

- WS68 : Fix handling of DOC_MANQUANT_LIST
- WS68 : Remove preference for RACC_DEVIS_A_PAYER and RACC_DEVIS_A_CONFIRMER
- WS34 : Fix missing index history ([DMC-870](https://ticketing.resa.be/browse/DMC-870))

## 3.7.3 / 2022-05-20

### FIXED

- WS68 : Remove preferences for template INDEX_WEB_ENREGISTRES
- WS126 : Fix error if "panne" is null
- WS25 : Correction of closed interruption
- WS25 : Fix status

## 3.7.2 / 2022-05-16

### FIXED

- WS120 : Fix query error

## 3.7.0 / 2022-05-10

### ADDED

- WS128 : Endpoint to fetch list of EP communes on /ep/communes

### MODIFIED

- WS120 : Add field "IdCommune" and adaptation to equi_id format

## 3.6.2 / 2022-05-09

### MODIFIED

- WS113 : Update url from digacert to elgacert

### FIXED

- WS23 : Correction of equi_id format

## 3.6.1 / 2022-05-06

### MODIFIED

- WS125 : Add filter "EnCours" to filter in progress interruption

### FIXED

- WS113 : Fix error if list of attachement

## 3.6.0 / 2022-05-05

### MODIFIED

- WS68 : Add "RACC_DEMANDE_ANNEXE" template
- WS125 : Add "Ep" filter
- WS23 : Store interruption location with coords in DynamoDB
- WS125 : Fetch interruption location and coords from DynamoDB
- WS26 : Fetch interruption location and coords from DynamoDB

### FIXED

- WS68 : Fix error when '\n' in a field

## 3.5.0 / 2022-05-03

### MODIFIED

- WS29 : Avoid null ReglInje/ReglPrel by copying normale meter one ([DMC-791](https://ticketing.resa.be/browse/DMC-791))
- WS59 : Return "RECU" for "OP" in the "RAC_E_PREQ_MES_PV" case ([DMC-667](https://ticketing.resa.be/browse/DMC-667))
- WS26 : Add "Ep" filed to identify EP related interruption
- WS125 : Add "Ep" filed to identify EP related interruption
- WS09 : Use new "Ep" filed from WS26 to check id EP or not

### FIXED

- WS19 : Fix error if multiple address returned by query
- WS29 : Fix error if multiple address returned by query

## 3.4.3 / 2022-04-28

### MODIFIED

- WS68 : Templates id modification ([RFS-4471](https://ticketing.resa.be/browse/RFS-4471))
- WS68 : Adapt PANNE_ID

## 3.4.2 / 2022-04-27

### MODIFIED

- WS26 : Match interruption to EP by address
- WS29 : Add field "CptControle" to identify control meter ([DMC-793](https://ticketing.resa.be/browse/DMC-793))
- WS59 : Return not mandatory for "OP" in the "RAC_E_PREQ_MES_PV" case ([DMC-667](https://ticketing.resa.be/browse/DMC-667))

## 3.4.1 / 2022-04-22

### MODIFIED

- Api Gateway : Allow unauthenticated cache invalidation from client
- WS113 : Update url from digacert to elgacert

### FIXED

- WS21 : Fix filter "FiltreEnPanne"
- WS14 : Remove empty sub-dossiers ([DevOps-3312](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3312/))

## 3.4.0 / 2022-04-21

### MODIFIED

- SAP API Management : Allow any OPTIONS call without api key
- WS21 : Add address number and match EP interruption by address ([DevOps-3184](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3184/))
- WS120 : Add address number and match EP interruption by address

### FIXED

- WS39 : Set url to null if unable to get it from Sharepoint instead of returning error 500

## 3.3.3 / 2022-04-13

### MODIFIED

- WS124 : Reduce keyword and id needed to stop interruption subscription ([DevOps-3166](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3166/))
- WS68 : Add mails for non-EP interruptions ([DevOps-3164](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3164/))

## 3.3.2 / 2022-04-01

### MODIFIED

- WS68 : Add templates for FICA mails ([DME-919](https://ticketing.resa.be/browse/DME-919))
- WS25 : Return EP EquiId ([DevOps-3161](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3161/))
- WS68 : Add interruption DE templates

## 3.3.1 / 2022-03-28

### FIXED

- WS23: Linking request response adapters fixed

## 3.3.0 / 2022-03-27

### ADDED

- WS125: Fetch list of interruptions with filters on /pannes ([DevOps-3099](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3099/))
- WS126: Search if an interruption exist for the given EP (by EquiId) or at an address on
  /pannes/active ([DevOps-3099](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3099/))

### MODIFIED

- Optimization : Use file cache on /tmp to avoir downloading file from S3 at each call.

### BUG FIX

- WS21 : Fix error 500 if no query params

## 3.2.1 / 2022-03-22

### MODIFIED

- WS21: Possibility to make call without filter ([DevOps-3124](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3124/))

## 3.2.0 / 2022-03-21

### MODIFIED

- WS26 : Add house number
- WS21 : Use live data from SAP ([DevOps-3094](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3094/))
- WS120 : Use live data from SAP ([DevOps-3094](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3094/))
- WS69: Adding the address of interruption in SMS communications ([DevOps-3082](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3082/))

### BUG FIX

- Add QLA web url and adfs info

### REMOVED

- Remove sync_ep lambda

## 3.1.0 / 2022-03-16

### ADDED

- WS124 : Endpoint to handle incoming SMS on POST /sms/incoming ([DevOps-3032](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/3032/))

### MODIFIED

- WS26 : Return info of any type of interruption (not only EP anymore) ([DevOps-2845](https://dev.azure.com/RESABE/SiteWeb_Refonte-RESA.be/_workitems/edit/2845/))

## 3.0.1 / 2022-03-14

### BUG FIX

- WS27 : Fix error for EAN without possible indexer passage information ([DMC-654](https://ticketing.resa.be/browse/DMC-654))

## 3.0.0 / 2022-03-07

### ADDED

- Deploy with SAP API Management as proxy

## 2.9.1 / 2022-02-28

### MODIFIED

- WS68 : Take langage preference from body before the user one
- WS09 : Send confirmation email/sms

### BUG FIX

- WS122 : Fix incorrect endpoint url
- WS34 : Fix error when only one result
- WS29 : Fix error with null "EQUNR"
- WS27 : Fix error with null in int value
- WS19 : Fix error when multiple counter info found
- WS34 : Change filter order to fix no result when last index "Meter reading reason" (EABLG.ABLESGR) value is not 01, 02, or 03

## 2.9.0 / 2022-02-25

### ADDED

- WS122 : Secured endpoint to remove interruption subscription on GET /panne_subscription/{PanneId}/remove

### BUG FIX

- WS25 : Fix pagination and status

## 2.8.1 / 2022-02-24

### MODIFIED

- WS68 / WS79 : Add DE templates

### BUG FIX

- WS25 : Fix pagination when no other params
- WS21 : Fix caching with DateDebut and DateFin

## 2.8.0 / 2022-02-23

### MODIFIED

- WS09 : Rework interruption subscription
- WS10 : Rework interruption un-subscription

### BUG FIX

- WS24 : Fix pagination
- WS25 : Fix pagination
- WS98 : Fix pagination
- sharepointUpload : Increase ram to handle image to pdf conversion with larger file

## 2.7.2 / 2022-02-23

### BUG FIX

- WS14 : Fix exception with SmartPortal check
- WS115 : Fix DynamoDB MD table access

## 2.7.1 / 2022-02-22

### MODIFIED

- WS120 : Add "Numero" and "CityCode" in the EP "Adresse"
- WS68 : Render subject template before storing mail copy in SharePoint
- /formulaire_remboursement : Add contract number validation

### BUG FIX

- WS24 : Fix call without filter

## 2.7.0 / 2022-02-21

### MODIFIED

- WS113 : Update digacert URL
- WS121 : Set "BpNom" and "BpPrenom" as mandatory
- WS21 : Add "DateDebut" and "DateFin" to filter EP by its last status date
- WS24 : Add possible call without filter

## 2.6.0 / 2022-02-17

### MODIFIED

- WS09 : Rework interruption subscription
- WS10 : Rework interruption un-subscription
- WS14 : Add call to check if SmartPortal enabled
- WS14bis : Add call to check if SmartPortal enabled

## 2.5.0 / 2022-02-14

### ADDED

- WS120 : Endpoint to get EP by id on /ep/<id>

### MODIFIED

- WS29 : Add "ReglPrel" and "ReglInje" separately for electric meter
- WS98 : Add "Zone" field, containing GeoJson zone of the city

## 2.4.1 / 2022-02-10

### BUG FIX

- signedUrlAuthorizer : Remove unwanted params added by SendInBlue

## 2.4.0 / 2022-02-10

### ADDED

- Add new authorizer signedUrlAuthorizer
- WS119 : Secured endpoint to set a user preference on GET /utilisateurs/edit/{Uid}/preferences

### MODIFIED

- WS68 : Add unsubscribe url

### BUG FIX

- WS24 : Fix error with 'GE' not mapped as 'Electrogene'

## 2.3.3 / 2022-02-18

### MODIFIED

- Onboarding : Enhance user BP research query
- Onboarding : Enable user BP research for production

## 2.3.1 / 2022-02-09

### MODIFIED

- /formulaire_remboursement : add creation date

## 2.3.0 / 2022-02-07

### ADDED

- Temporary endpoint to receive user bank information for energy refund on /formulaire_remboursement

### MODIFIED

- WS113 : Add type in name when storing Digacert documents

## 2.2.3 / 2022-02-03

### BUG FIX

- UserActivation : Avoir calling /sap/utilisateur/recherche on production

## 2.2.1 / 2022-02-03

### BUG FIX

- UserActivation : Fix lambda permission error

## 2.2.0 / 2022-02-02

### MODIFIED

- UserActivation : Call WS73/74 directly with lambda to avoir 30s timeout
- WS01/WS02 : Set phone to null if only +32
- WS14bis : Filter and order of EAN counter

## 2.1.2 / 2022-02-01

### BUG FIX

- UserSync : Check EAN from WS27 is from same user before adding them
- WS117 / WS59 : Fix int instead of string in SQL query
- WS14bis : Fix EAN history and order

## 2.1.1 / 2022-02-01

### MODIFIED

- WS73/74 provide BpEmailCom
- update phone checking pattern
- regroupement bp handle organisation case matching

## 2.1.0 / 2022-01-28

### MODIFIED

- WS14bis : Return EAN history of lasts 3 years in "History"
- WS14bis : Add "SmartPortal" boolean if any smart counter used in the last 3 years
- UserActivation : Remove "Bienvenue sur MyResa" notification

## 2.0.4 / 2022-01-27

### BUG FIX

- WS03 : Fix sending of password reset email

## 2.0.3 / 2022-01-27

### BUG FIX

- WS14bis : Fix case and error if no query params
- WS59 : Fix missing values after optimisation

## 2.0.2 / 2022-01-26

### MODIFIED

- WS59 : Optimization with parallel call to post query actions
- WS14bis : Adaptation and optimization for website usage

### BUG FIX

- WS01 : Use 'BE' as default for "CodePays"
- WS59 : UserInfo fix query error

## 2.0.1 / 2022-01-26

### BUG FIX

- Fix issue on WS74

## 2.0.0 / 2022-01-25

### BREAKING CHANGES

- WS69 : Add basicauth
- WS58 : Add basicauth
- /monitoring : Add basicauth

### MODIFIED

- WS59 : UserInfo now return NumDossier also if only email match

### BUG FIX

- WS34 : Fix index for total hours tarification

## 1.4.7 / 2022-01-24

### BUG FIX

- Fix issue on regroupement bp

## 1.4.6 / 2022-01-20

### BUG FIX

- WS79 : Fix error with mail
- WS68 : Find user by lower mail
- WS68 : MYRESA_INCONNU force mail for ghost user
- WS01 : Fix ghost account creation

## 1.4.5 / 2022-01-17

### MODIFIED

- UserSync : Enhancement of query to find EAN/dossier for better result
- WS03 : Check if email already exist in AD before sending activation mail
- UserActivation : Set BP_ORIGINE = MyRESA at user onboarding

### BUG FIX

- WS39 : Return prices as string with 2 decimal
- WS19/27/29/33/34/59/63 : Fix parallel controller

## 1.4.4 / 2022-01-11

### ADDED

- a first version of /me/dashboard/bis aim to replace /me/dashboard with BP internal logic

### MODIFIED

- Activation procedure is looking to attach an existing bp before creating a new one

## 1.4.3 / 2022-01-10

### MODIFIED

- WS68 : Set '<EMAIL>' as default 'reply_to' in mails

## 1.4.2 / 2022-01-06

### MODIFIED

- Switch API from QTL to QLA
- WS96 : Hide "Authorization" header on request and response

## 1.4.1 / 2022-01-05

### MODIFIED

- WS39 : Add "MontantRestant" with the price remaining to pay

### BUG FIX

- WS68 : Fix "ReplyTo" error when uploading file to sharepoint
- WS68 : Fix preference error

## 1.4.0 / 2022-01-04

### MODIFIED

- WS01 : Set user preference "com_dossier_racc_mail" to true by default
- WS68 : Force list for 'ContentA' and 'DOC_MANQUANT'
- WS68 : Set "ReplyTo" attribute with sendinblue
- GhostCleaner: Check for com_encod_index_mail and email fiels in user preferences

### ADDED

- WS118 : Endpoint to retrieve civility on /referentiel/civilite

## 1.3.3 / 2021-12-24 (Merry Christmas 🎝)

### MODIFIED

- WS68 : Set back custom upload on sharepoint valise (removed when "Valise" field was added)
- WS04 : Compact all preferences of user with same mails and use it as base preferences for the new user
- WS68 : Only use active user preference or most recent ghost

### BUG FIX

- WS68 : Fix error when no "Header.VALISE" field
- WS68 : Fix uploading multiple time same file to SharePoint

## 1.3.2 / 2021-12-23

### MODIFIED

- WS68 : Transform string with '\n' into list of string
- WS68 : Add template 'RACC_PLANIF_VISITE_RACC'
- WS68 : Add template 'RACC_DOC_MANQUANT'
- WS59 : Sort dossiers by id
- UploadSharepoint : In case of failure, keep upload of original file to avoir losing it

### BUG FIX

- WS68 : Fix error with "MYRESA_INCONNU" when no phone template
- WS117 : Fix returning EAN not currently active
- UploadSharepoint : Fix removal of alpha channel in images

## 1.3.1 / 2021-12-22

### MODIFIED

- WS59 : Add "TypeDossierCode" to identify new or modification
- WS117 : Add protection with basicauth
- WS68 : Use "Valise" field to send a copy of the communication to SharePoint valise
- WS68 : Use "Header.MYRESA_INCONNU" to allow sending communication to unknown user

### BUG FIX

- Fix connection error on database affecting some endpoints

## 1.3.0 / 2021-12-21

### ADDED

- WS117 : Endpoint to retrieve EAN based on user information on /ean/recherche

### ADDED CASE TO EXISTING WS

- UserSync : Use WS117 with "UserInfo" to retrieve eans

### REMOVED

- UserSync : Remove sync OR => EAN
- UserSync : Remove sync Dossier => EAN
- UserSync : Remove sync EAN => Dossier

## 1.2.5 / 2021-12-20

### BUG FIX

- WS59 : Fix 404 with query "UserInfo" when no data in ADR2 or ADR6
- WS113 : Remove base64 file from response

## 1.2.4 / 2021-12-17

### MODIFIED

- UserSync : Add filters on dossiers before adding to user
- WS59 : Use also "ADR2.TELNR_CALL" to find dossiers with "UserInfo"

### BUG FIX

- digacertStoreCert : Fix missing dependencies
- WS20 : Fix error on some adresses that have same zip code for 2 city
- WS68 : Fix adfs web env var

## 1.2.3 / 2021-12-16

### MODIFIED

- WS68 : Change for racc templates
- WS68 : Add template INDEX_WEB_ENREGISTRES

### BUG FIX

- WS59 : Fix MONT_DEVIS sql query error
- WS68 : Fix missing env var
- WS43 : Fix column used for locality

## 1.2.2 / 2021-12-15

### MODIFIED

- WS68 : Add "ClientId" and "DomainName" to data for SendInBlue

### BUG FIX

- WS59 : Fix general error
- WS68 : Fix error when data with only one "partenaire"
- WS59 : Fix "UserInfo" sql query
- WS59 : Fix remaining amount to pay for "étude"

## 1.2.1 / 2021-12-14

### MODIFIED

- WS68 : Add templates config for other racc mails
- WS109 : UPLOAD_DOC actions, remove filter on uid
- WS59 : Add "Quick" query params to retrieve info without actions and sub-dossiers infos
- WS59 : Dossier in step 9 that return to its sub-dossier step, now also copy its actions list

### BUG FIX

- SharepointUpload : Fix wrong dossier id set in valise
- SharepointUpload : Fix date missing in document name
- WS39 : Fix incoherent file name
- WS39 : Fix error when only one file in sharepoint valise
- WS116 : Fix returned error for non user dossier (use 403 instead of 401)
- WS59 : Fix error with "UserInfo" query
- UserSync : Also retrieve sub-dossier in WS59 with "UserInfo"

## 1.2.0 / 2021-12-13

### ADDED CASE TO EXISTING WS

- WS59 : New "UserInfo" to retrieve dossiers based on user information
- UserSync : Use WS59 with "UserInfo" to retrieve dossiers

### BUG FIX

- WS115 : Fix call to sharepointUpload on WS35 callback

## 1.1.1 / 2021-12-09

### BUG FIX

- WS116 : Fix variables scopes inside python lambda function

## 1.1.0 / 2021-12-09

### ADDED

- WS116 : Endpoint to download dossier linked documents on /demande_travaux/documents

### MODIFIED

- WS77/109 : Send files to SharePoint when WS109 called with ACTION=UPLOAD_DOC
- WS77 : Send files when WS35 callback is triggered

### BUG FIX

- WS39 : Fixed url to download document
- WS77 : Remove double extension when uploaded on SharePoint
- Onboarding : Fix user creation error

## 1.0.1 / 2021-12-08

### MODIFIED

- WS59 : Remove check "RAC_PREQ_MES_MVI = O" for CTRL_ADRC1/9/10and CTRL_ADMC1/3/4
- WS59 : Step 81 go to 85 if no date possible

### BUG FIX

- WS59 : Fix error for dossier with multiple date in table QMSM
- WS59 : Set head dossier that is at step 9 to its higher sub dossier step

## 1.0.0 / 2021-12-07

### MODIFIED

- WS79 : Add mail adresses to use for production

## 0.11.5 / 2021-12-06

### BUG FIX

- WS110 : Use "DateAccord" when calling WS109
- WS59 : Return CTRL_ADMC actions in step 7
- WS59 : DATE_PLANIF actions, handle STATUT1 = MESE

## 0.11.4 / 2021-12-03

### SECURITY FIX

- WS95 : Remove possibility to call external URL

## 0.11.3 / 2021-12-03

### MODIFIED

- WS59 : Use ATFLV values for some racc config
- WS59 : Handle "Etude"
- WS110 : Handle "Etude"
- WS59 : Do not return dossier that are already present in sub dossier list of another head dossier
- WS59 : Set head dossier that is at step 9 to its higher sub dossier step
- WS14 : Improve performance

### BUG FIX

- WS59 : Change "DateDossier" value
- WS59 : Remove "CTRL_ADRC" for step 8
- WS110 : Fix WS109 call in Mollie callback
- WS27 : Fix caching

### SECURITY FIX

- WS29 : Remove "Bp" field to avoid data leak

## 0.11.2 / 2021-12-02

### BUG FIX

- WS77/112 : Fix CORS when uploading files
- Bad int conversion for data from DynamoDB
- WS68 : Bad SharePoint valise for racc mail storage

## 0.11.1 / 2021-12-01

### ADDED

- WS95 : Set process status to "FAILED" if the endpoint fail directly

## 0.11.0 / 2021-11-30

### ADDED

- WS115 : Endpoint to post async result on /asynchrone/callback

### MODIFIED

- WS95 : New parameter "Type" to handle call to endpoint already async that will use on /asynchrone/callback to post result

### SECURITY FIX

- WS27: Remove Nom and Prenom to avoid data leak

## 0.10.0 / 2021-11-26

### BUG FIX

- WS68 : Fix multiple save in sharepoint for racc mail that cause timeout
- WS68 : Fix ContentC containing "Type" without "Valeur" error
- WS68 : Force utf-8 encoding on received message

### MODIFIED

- WS77 : Use S3 presigned url for file upload
- WS112 : Use S3 presigned url for file upload

### SECURITY FIX

- WS29: Set Email and Tel to null to avoid data leak

## 0.9.0 / 2021-11-22

### ADDED

- WS113 : Endpoint for Digacert soap actions on /digacert/soap
- WS114 : Endpoint for possible power for fairs on /demande_travaux/puissance/forains
- Job to clean unused ghost users after some time

### MODIFIED

- WS68 : Upload racc mail to sharepoint

## 0.8.13 / 2021-11-12

### MODIFIED

- WS68 : Add smartportal DE templates
- WS68 : Return best global status instead of worth (now 200 if one communication is ok)
- WS68/12 : Change smartportal preferences
- UserSync : Now run asynchronously

### BUG FIX

- WS73-74 : Fix NumCmpt error

## 0.8.12 / 2021-11-09

### BUG FIX

- WS68 : Clean dict keys whitespaces
- WS112 : Encode filename in utf-8 to avoid non printable unicode character error

## 0.8.11 / 2021-11-04

### MODIFIED

- WS79 : Add templates for new case MODIFICATION RACCORDEMENT/DEMANDE RACCORDEMENT AUTRE

## 0.8.10 / 2021-11-03

### BUG FIX

- WS27 : Fix error with bearer token

## 0.8.9 / 2021-11-03

### MODIFIED

- WS27 : Fix with EAN with no contract at all
- WS68 : Add template config for racc modification RACC_MODIFICATION_DOSSIER

## 0.8.8 / 2021-11-02

### MODIFIED

- WS27 : Return info of inactive EAN and add a "Statut" field ACTIF/INACTIF
- WS29 : Return info of inactive EAN

### BUG FIX

- Fix refresh AD key
- Fix version mail zip not working properly on Windows

## 0.8.7 / 2021-10-29

### BUG FIX

- WS77 : Trim IdDossier to remove non printable unicode character
- SharepointUpload : Encode soap body to utf-8 to avoid unicode character error

## 0.8.4 / 2021-10-27

### MODIFIED

- WS79 : Add template client for "NOUVEAU RACCORDEMENT/DEMANDE RACCORDEMENT AUTRE"

## 0.8.3 / 2021-10-27

### BUG FIX

- WS68 : Fix SAP call made with a single dict {'Valeur':xx, 'Type':xx}
- WS112 : Fix s3 write right

## 0.8.2 / 2021-10-25

### BUG FIX

- WS112 : Fix missing import

## 0.8.0 / 2021-10-25

### ADDED

- WS112 : New WS upload temp files

### MODIFIED

- WS79 : Adapt to use files url
- WS68 : Special case for "ComDossierRacc" mails, send also to CONTACT and DEMANDEUR

## 0.7.6 / 2021-10-25

### MODIFIED

- WS79 : Add client return email

## 0.7.5 / 2021-10-22

### MODIFIED

- WS97 : Handle user preferences in the patch
- WS79 : Add attachment for SendInBlue email
- WS63 : Remove NbCpt from request

## 0.7.3 / 2021-10-22

### MODIFIED

- WS77 : Handle image with alpha in image to pdf conversion
- WS79 : Use SendInBlue to send some email

## 0.7.2 / 2021-10-19

### MODIFIED

- Add ApiGateway binary_media_types in deploy

### BUG FIX

- WS59 : Fix possible loop error
- User Sync fix for wrong EAN

## 0.7.1 / 2021-10-15

### MODIFIED

- WS68 : Implement RFS-907 : Send mail/sms message to SharePoint
- WS68 : Add template config for SmartPortal
- WS68 : Add template config for RACC_CREATION_DOSSIER
- WS59 : Add 'DossierInf' with dossier data
- WS59 : Return CTRL_ADRC also for step 5,7,8
- UserSync : fix issue with address matching

## 0.6.1 / 2021-10-11

### BUG FIX

- WS20 : Fix 'Localite' field error

## 0.6.0 / 2021-10-08

### ADDED

- WS111: Get country list

### MODIFIED

- WS59 : Add "NumDossierInf" with list of dossier number

### BUG FIX

- WS59 : Fix paid amount returned as 'Etude'
- WS59 : Fix "montant restant du" null when no payment
- WS59 : Fix "TypeInfo" value and state 45 conditions

## 0.5.0 / 2021-10-06

### MODIFIED

- WS59 : Change 'CodeStatut' to 85 for some Elec dossier
- WS59 : Change ADRC linking between dossier and documents

### BUG FIX

- WS59 : Set correct 'LibStatut' if 'CodeStatut' modified
- WS39 : fix paid amount returned as 'Etude'

### Payment flow changes

#### MODIFIED

- WS39 : remove payment info from DynamoDB
- WS109 : update spec, now accept the DEVIS_PAYE action
- WS110 : remove payment info saving in DynamoDB
- WS110 : add call to WS109 with DEVIS_PAYE action when status is paid

## 0.4.3 / 2021-10-06

### BUG FIX

- WS59 : remove old code that set to statut 11 for PLANIFIABLE action
- WS20 : fix gaz not found

## 0.4.2 / 2021-10-04

### MODIFIED

- WS20 : Add distance from gaz pipeline (RFS-372)

## 0.4.1 / 2021-09-28

### MODIFIED

- WS20 : municipality instead of city for "Localite"

## 0.4.0 / 2021-09-27

### MODIFIED

- <span style="color:red">Switch from QTE to QTA again</span>

## 0.3.1 / 2021-09-24

### MODIFIED

- WS20 : Add "IsGRDElec" and "IsGRDGaz"

## 0.3.0 / 2021-09-22

### MODIFIED

- <span style="color:red">Switch back from QTA to QTE</span>

## 0.2.3 / 2021-09-21

### BUG FIX

- WS59 : Fix "LibInfo" splitting
- WS39 : Fix not found error in case of an "Etude"

## 0.2.2 / 2021-09-20

### MODIFIED

- WS17 : Use municipality instead of city for "Localite"

## 0.2.1 / 2021-09-20

<b><span style="color:red">
New version number following [Semantic Versioning 2.0.0](https://semver.org/) recommendations
</span></b>

### MODIFIED

- WS39 : store sharepoint document in S3 and return a s3 pre-signed url instead
- WS27 Allow usage without NumCpt
- WS59 : split "LibInfo" in two field "LibInfo" and "LibInfoStatut"

## 0.1.101 / 2021-09-15

### MODIFIED

- <span style="color:red">Switch from QTE to QTA</span>
- WS77 : allow for ghost users (remove no auth access)
- WS78 : allow for ghost users (remove no auth access)

## 0.1.100 / 2021-09-14

### BUG FIX

- Fix authorisation error
- WS68 : send mail to contact_email instead of (connection) email

### MODIFIED

- WS109 : record in DynamoDB the list of actions executed
- WS59 : add field WS109Action to list all WS109 executed actions
- WS20 : Switch from passthrough to SqlDriven
- WS11-12 : add preference for smart portal communication

## 0.1.97 / 2021-08-19

### MODIFIED

- WS77 : allow for non auth users
- WS78 : allow for non auth users

### BUG FIX

- Fix basic authorizer for some endpoint

## 0.1.96 / 2021-08-19

### MODIFIED

- WS59 : Handle different languages correctly
- WS59 : add TypeFluide to response
- WS68 : Allow GripPocket basic auth

### ADDED

- WS68 : add confirmed sms templates

### BUG FIX

- WS59 : Fix error with dossier at CodeStatut 42

## 0.1.95 / 2021-08-18

### BUG FIX

- WS39 : Fix error caused by Mollie changes

## 0.1.94 / 2021-08-17

### ADDED

- WS111+WS112 : Add GET + POST /me/consentements for Smart Portal

### MODIFIED

- WS68 : Enhancement to call with classic json + search matching user on all provided field

### BUG FIX

- remove all import \* to avoid future unresolved reference
- Fix error 500 on /me/dashboard if details of a Dossier is not found

## 0.1.93 / 2021-08-12

### BUG FIX

- WS19 : Fix error with NumCompl conversion to int (keep it as string)
- WS73 internal call : handle parallels call error

### BREAKING CHANGES

- WS59 : Rework for new spec v0.9
- WS53 : deleted /facturation/payements

### ADDED

- WS110 : Add callback for mollie payment status on /payements/mollie/update

### MODIFIED

- WS59 : add TypeDossier to response

## 0.1.92 / 2021-07-29

### BUG FIX

- fix minor deployment issue

## 0.1.90 / 2021-07-14

### BUG FIX

- fix powalco issues

## 0.1.89 / 2021-07-09

### BUG FIX

- WS68 : convert every value to string before calling sendinblue

### MODIFIED

- WS68 : add templates config for (de)activation of port P1
- WS68 : add templates config for index SMS
- WS60 : Return to passThrough
- WS73/WS74 : Ean field added, and CodePrefe removed
- Lambda userSynchronisation (used by WS that impact EAN, like the WS05) : call to WS74 to update user data after processing
- WS77-78 : rework to handle "dossierId" and fix connection to sharepointUpload

## 0.1.87 / 2021-06-30

### BUG FIX

- Fix an error that caused crash on some call with null body

## 0.1.86 / 2021-06-29

### BUG FIX

- Fix permission for the lambda that send new api version mail
- WS29 : check link between meter and protection still active

### ADDED

- WS109 : passThrough to SAP WS109 on /demande_travaux/confirmation

### MODIFIED

- remove API check on unused SAP WS
- WS34 : Add "CatCadranLibelle" and "CatTarifLibelle" in the result

## 0.1.85 / 2021-06-28

### BUG FIX

- Fix deployment error

## 0.1.83 / 2021-06-25

### MODIFIED

- New mail on API new version deploy (qtl and prod)
- WS68 : Only send mail/sms in production

### BUG FIX

- WS33 : Fix import error

## 0.1.82 / 2021-06-16

### MODIFIED

- WS77 and WS79 : convert image files to PDF
- WS23 : Adapt EquiID before calling SAP
- WS25.1 : add zero filling to the id if needed
- WS34 : refactoring
- WS34 : add "Consommation" attribute to the output (annual consumption)

### BUG FIX

- WS34 : fix output structure that wasn't always coherent
- WS64 : Sandbox parameter shouldn't be mandatory

### BREAKING CHANGES

- WS68 : adapt input for a more flexible system to fill template and use sendinblue to send mail

## 0.1.81 / 2021-05-31

### ADDED

- WS107 : /ean/validation endpoint to validate ean format and checksum

## 0.1.78 / 2021-05-19

### MODIFIED

- WS35 : no more need to be logged-in
- WS25 : return unplanned outages ID
- WS101 : limit sent sms to 3 max
- WS16 : Remove onboarding from /authenticate

### ADDED

- WS25.1 : /pannes/unplanned/:id get en unplanned outage by ID

### BUG FIX

- New user can no more start account activation process more than once

## 0.1.76 / 2021-05-18

### MODIFIED

- Enable multiple environment for the smart gaz pin by sms

### ADDED

- Regular (every month) job to check user coherence between AD and DynamoDB
- WS106 : /adresse/cdpostaux get all postal code and cities where Resa is active

## 0.1.73 / 2021-04-23

### ADDED

- Retrieve smart gaz pin by sms

## 0.1.70 / 2021-04-14

New onboarding version

### ADDED

- GET /adress WS17. Added City code for missing entries.

### BUG FIX

- GET /index should save EAN in user profile (regression due to onboarding changes)

### ADDED

- WS63: Refactored - 1.7s/call performance improvement (0.9s vs 2.6s)

## 0.1.60 / 2021-03-10

### BREAKING CHANGES

- WS43: La Langue est passée en paramètre requis du header au lieu de paramètre optionnel en query string

### WS39

- In order to support queries by PartenaireId AND/OR by DossierId the URL is not anymore
- /demande_travaux/{PartenaireId}&Ordre=<OrdreId> but instead
- /demande_travaux/devis?PartenaireId=<PartenaireId>&Ordre=<OrdreId>
- Bug fix: WS39 was interpreting the Partner + Order filter as an "OR", should be an "AND"
- Performance improvement

### BUG FIXES

- WS26 fixed bug that made it crash.

## 0.1.51 / 2021-03-04

WS05:

- Require NumCpt
- If there are already EANs in the profile, user full name and contact information of the input ean have to match at least one existing ean

Onboarding

- Require full name and contact information match between the partenaire linked to dossier and the partenaire linked to Ean in Ean -> Dossier sync.

### ADDED

- Modified link EAN-compteurr defined in WS 27 28, 29, 34, 60 as indicated by X.Locht

## 0.1.51 / 2021-03-03

### ADDED

- User has an optionnal field `PhoneFixe`

## 0.1.51 / 2021-02-18

### BREAKING CHANGE

- /ean/desc replaces WS29 /ean
- We now request for NumCpt when calling WS29

## 0.1.50 / 2021-02-04

### ADDED

- WS59: Refactored - performance improvements

* Simple call (get info related to 1 dossier) performance improved by 1s (1.6s vs 2.6s)
* Heavier call (get info from 40 dossiers) performance improved by 6s (3s vs 9.1s)

## 0.1.49 / 2021-02-03

### ADDED

- Fix WS64 token case issue
- translate error messages in french

## 0.1.48 / 2021-02-01

### ADDED

- Fix boolean on WS74-75
- set some rror messages

## 0.1.45 / 2021-01-26

### ADDED

- call to WS74 when user's data change
- improve check on user activation

## 0.1.44 / 2021-01-22

### BREAKING CHANGE on WS14 /me/dashboard

- Fixed nomenclature of PPP flag into Ppp

## 0.1.43 / 2021-01-21

### FIXED

- issue with PATCH /utilisateurs

## 0.1.42 / 2021-01-21

### BREAKING CHANGE on WS14b /me/dashboard

- Refactor: The PPP flag is not anymore inside ListeEan, but at the root object level
- Fix: the PPP flag should be true if there is a Master PPP Id
- Update: language setting in AD depending on language preferences of the user
- Refactor: IdPpp is a legacy attribute and has been removed

## 0.1.41 / 2021-01-06

### MODIFIED

- New flux PPP

## 0.1.41 / 2020-12-08

## 0.1.40 / 2020-12-14

## BREAKING CHANGE - MODIFIED WS34

- The URL is modified in order to handle new input parameters. It is now /index/historique instead of /index/{MeterId}
- The current endpoint accepts 3 different types of inputs Ean, NumCompteur, or both Ean and NumCompteur
- Performance improvements: < 1s instead of ~5s

## BREAKING CHANGES WS24 and WS25

- Added GPS geocordinates based filtering.
- For WS24 only: a panne can be linked to multiple positions. Both average coordinates and the list of positions are given.
- Added a pagination. Output is now wrapped in a Data container, as for the WS23
- Performance improvements

## 0.1.40 / 2020-12-11

## BREAKING CHANGE in WS26

- Instead of providing the DernierStatut of the panne, we provide the HistoriqueStatut, sorted in descending order

## 0.1.40 / 2020-12-17

WS71 /adresse/distCourte
Added IdRadRue and IdRadLocalite to output.

## 0.1.41 / 2020-12-18

# ADDED in WS27 and WS29

- IdRadRue and IdRadLocalite of objet de raccordement

## 0.1.40 / 2020-11-25

### FIXED WS27 /ean

- There was a typo in attribute name NumpCpt should be NumCpt

### FIXED WS33 /index/passage

- Ean detruit was not being corrctly checked

### MODIFIED WS27 /ean

- New attributes are added in output: BpHgz, CcHgz, StatutEan (via WS33)

### MODIFIED WS33 /index/passage

Added StatutEan

### Improvements to /me/dashboard

- Replace call to WS29 by call to WS27
- Objets de raccordement and PartenaireIds are fetched from WS27 response, not from MyResa database

### Improvements to /index/preparer_encodage

- Replace call to WS29 by call to WS27
- Compteurs are fetched from WS27 response, not from MyResa database

## 0.1.39 / 2020-11-23

### MODIFIED WS27 /ean

- Améliorations de performance uniquement
- Des informations qui étaient présentes uniquement dans le WS29 sont maintenant présentes dans le WS27 aussi

* Données personnelles du Bp (telephone email)
* PartenaireId et informations sur le Contrat (numéro de contrat, Move-in, Move-out)
* Objet de racccordement
* Fournisseur
* Fréquence du compteur
* Code Nace si Partenaire est une société (et disponible)
* Type d'installation

### MODIFIED WS28 /ean/cpt

- Améliorations de performance
- NumCompteur peut aussi etre un string (pas nécessairement numérique). Ceci est nécessaire pour les compteurs smart.

### ADDED L'endpooint /ean/desc/{Ean} est une réimplémentation de l'endpoint WS29 /ean/{Ean}. Les deux

endpoints coexistent dans cette version de l'API, sans aucun changement de /ean/{Ean}

- Amélioration substantielle de performance (< 4s maintenant, avant des time-out pouvaient se produire)
- L'informations liée aux compteurs est contenue dans un sous-objet Compteurs.
- De nouvelles informations sont ajoutées: SectActivitie (type d'énérgie), Mi (date de move-in)
- Les informations fournies sont adaptées au type d'énérgie et au tarif (de nombreux attributs ne sont pas relevant pour gaz, et ne sont
  pas affichés lorsque l'EAN est un EAN gaz)
- Une description de HoraireTarif pour les EAN Elec Double horaire n'est PAS présente et doit etre rajoutée
- Une description de FctCompt n'est PAS présente et doit etre rajoutée
- Pour les compteurs gaz ComptCali n'est PAS présent et doit etre rajouté

## MODIFIED WS33 /index/passage

- Améliorations de performance
- Admet d'autres inputs que un EAN:

* Un EAN et un NumCompteur (qui devra etre consistent avec l'EAN ou message d'erreur)
* Un NumCompteur
* Un EAN

## MODIFIED onboarding procedure

- Améliorations de performance./
- Améliorations fonctionnelles: on garantit que toutes les données sont rapatriées en un seule execution de la procedure

## 0.1.37 / 2020-11-12

### ADDED

- Adding `EmailExist` field in WS64
- Passing passthrough internal calls to HTTPS
- POST facturation/payements
- POST /sms/send_code AND GET /sms/check_code

## 0.1.36 / 2020-10-26

### FIXED

- issue with WS34
- minor issue with WS64

## 0.1.34 / 2020-10-23

### FIXED

- fix PATH /utilisateurs

## 0.1.33 / 2020-10-22

### FIXED

- fix updateMail

## 0.1.32 / 2020-10-22

### ADDED

- WS79 formulaire `Autre/Contact générique`
- Welcome notification on user activation
- WS94 POST /token/invalider

### FIXED

- Notification filter issue
- Issue with WS64 /token

## 0.1.24 / 2020-10-09

### ADDED

- /ep and /pannes/:id do not consider an avis as a panne when priority of avis is not EP Tournée curative
- /ep fixed wrong status label for equipments that have a panne closed via order and not via avis

## 0.1.23 / 2020-10-06

### ADDED

- /ep and /pannes/:id use PTE SAP environment

## 0.1.22

### ADDED

- GET /pannes/tad

## 0.1.17 / 2020-09-23

### ADDED

- Implement API versionning for prod environements

### ADDED

## 0.1.14 / 2020-09-22

- Refresh of points de recharge ready for production

## 0.1.16 / 2020-09-15

### ADDED

- add GPS geoloc to /pannes/planned & /pannes/unplanned
- /asynchrone and /asyncrone/{Id} endpoints

## 0.1.15 / 2020-09-14

### ADDED

- NumCpt in POST /me/ean
- WS21/22 /ep Added id panne in response
- WS26 /panne/:id admits an integer as id

### BREAKING CHANGE

- WS64: The web service was not responding. It has been fixed.

## 0.1.14 / 2020-09-04

### ADDED

- WS62: ADD /facturation/pdf
- PATCH /utilisateurs

# REMOVED

- /me/contact (replaced by PATCH /utilisateurs)

### UPDATE

- /sap/user/create devient -> api.resa.be/v1/sap/utilisateur/creation
- /sap/user/edit devient -> api.resa.be/v1/sap/utilisateur/modification
- /sap/user/delete devient -> api.resa.be/v1/sap/utilisateur/suppression
- Default user preferences

## 0.1.13 / 2020-08-24

### FIX

- Solved issue with encoding

### ADDED

- WS77: api.resa.be/v1/fichiers/upload: upload a file for "demande de raccordement"
- WS78: WS78 GET api.resa.be/v1/fichiers/liste: list the files uploaded in a "demande de raccordement"
- WS79: api.resa.be/v1/formulaire/demande: POST a "formulaire de demande"
- WS14: Add Numéro de contrat, Remove duplicate in "numéro de compteur" attached to an EAN, Show only "numéro de compteur" in status "actif", Add the 2 PPP Ids provided by WS64 (
  added during onboarding process), Add dossier data (WS59 Body) in WS14 ("dossier" section)
- WS45: Remove "-" in amount returned
- WS46: Remove "-" in amount returned
- WS31:Inject error code provided in response body in HTTP error code
- WS34: "Nom" et "Prénom" are not required anymore, only EAN
- WS29: ADD field "UCodeLibelle"
- WS61: "Missing parameter" error fixed
- Notification: Examples of "Welcome" notification have been added for Yolande et Lionel
- WS06: Format of the response has been adapted as requested. Important note: WS06 can return several users if the deleted EAN was linked to several users

### BREAKING CHANGE

- WS06 - Update format of Response
- WS06 - Correct issue in userdata.py

### DISCLAIMER

- Endpoint to upload PDF documents to Sharepoint temporarily down

## 0.1.12 / 2020-08-20

### BREAKING CHANGE

- WS05 The request body expected is not anymore a pair Ean, Cpt but the Ean alone (see documentation)

## 0.1.12 / 2020-08-20

### ADDED

- UPDATE WS29 add field "UCodeLibelle"

### CHANGES

- Update API call to avoid Nom and Prenom for WS34

### BREAKING CHANGES

- /token Change structure and require ghost auth
- WS05 The request body expected is not anymore a pair Ean, Cpt but the Ean alone (see documentation)
- Update Notification structure : Type, Priority, Link, ReadTimestamp
- WS06 - Update format of Response
- WS06 - Correct issue in userdata.py

## 0.1.11 / 2020-08-12

### FIXED

- Error with /facturation/contrats/{Id} and facturation·/contrats

## 0.1.10 / 2020-08-11

### BREAKING CHANGES

- Deprecated Cpt in /me/ean
- /me/dashboard ListeEan Cpt is a list of string

### ADDED

- /me/dashboard return a PartenaireId for each ean
- RESAAPI-343 344 348 use PATCH instead of POST develop filter for GET notifications and PATCH notifications status in dashboard file

## 0.1.9 / 2020-08-05

### ADDED

- RESAAPI-397
  In the response, the Nones should not be strings i.e. "None" but nulls.
  Change in the class dashboard in getDynamoDB.

## 0.1.8 / 2020-08-06

### FIXED

- Issue with BasicAuthorizer fort /envMessage

## 0.1.7 / 2020-07-24

### ADDED

- POST /token

## 0.1.6 / 2020-07-24

### BREAKING CHANGES

- `GET /ean` (i.e `WS27`) returns new attributes, retrieved from `GET /index/passage` (i.e `WS33`). The new attributes are
  `DateDebut`, `DateFin`, and `CptSmart`

## 0.1.5 / 2020-07-20

### ADDED

- POST /portP1/demande
- GET /portP1

## 0.1.4 / 2020-07-17

### ADDED

- More info about Ean & Dossier in dashboard

## 0.1.3 / 2020-07-16

### ADDED

- ADD read status of /notification/status

## 0.1.2 / 2020-07-15

### DISCLAIMER

We detected a bug in the Swagger UI software used to document the API. The last 2 digits of the EAN introduced by the user in the UI are sometimes
replaced by 0 in the request performed by Swagger (as seen the curl sent by Swagger in the UI). This is for instance the case for POST /me/ean

### ADDED

- Use caching
- WS14 GET /me/dashboard returns new attributes: related to EAN, to demande de travaux (dossier), profil, preferences,
  notifications. Note that for the time being there is no endpoint populating notifications so it will always be empty.
- DELETE /utilisateurs/ean/{Ean}

### REMOVED

- DELETE /me/ean/{Ean}
- DELETE /utilisateurs/edit/{Uid}/ean/{Ean}

### BREAKING CHANGES

- GET /ean/cpt and GET /pannes/unplanned : move body to querystrings
- WS35 /demande_travaux/demande GET -> POST

### BUG FIX

- /ep Les luminaires dont la longitude et latitude est inconnue mais encodée comme `"Lat": 2.3066892917310384, "Long": 49.29333480390865` n'apparaissane plus dans la response
- /ep Quand `FiltreEnPanne=true`, on ne garde que des luminaires avec un panne active

## 0.1.1 / 2020-07-03

### ADDED

- PartenaireIds and IdPpp fields in /me/dashboard

### FIXING

- Solve bug with activated user

### BREAKING CHANGES

- /pannes/planned POST -> GET

## 0.1.0 / 2020-06-26

### ADDED

- POST /adresse/match

### BREAKING CHANGES

- /index/passage : POST -> GET & data in querystrings
- POST /ean -> GET /ean/cpt
- /pannes/planned GET -> POST
- /pannes/unplanned POST -> GET
- /demande_travaux/demande POST -> GET
