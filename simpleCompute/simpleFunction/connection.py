import json
import os
from concurrent import futures

from utils.api import api_caller
from utils.errors import BadRequestError
from utils.file_utils import get_file_extension, download_file_in_memory, upload_file_in_memory
from utils.models.connection_model import Connection, WorkType


def create(event, context):
    body = event.get("body")
    headers = event.get("headers")

    session_id = headers.get("SessionId")
    token = headers.get("Authorization")
    if not session_id and not token:
        raise BadRequestError("SessionId or Bearer token not provided", error_code="MISSING_SESSIONID_OR_BEARER")

    connection_request: Connection = Connection.model_validate_json(body)

    # Set preference to receive racc mail to true. If the user is a ghost, then set is email as well.
    if session_id:
        api_caller(
            "PATCH",
            "/utilisateurs",
            body={"Email": connection_request.applicant.email, "ContactEmail": connection_request.applicant.email},
            headers={"SessionId": session_id},
        )
        api_caller(
            "POST",
            "/me/preferences",
            body={"com_dossier_racc_mail": True},
            headers={"SessionId": session_id},
        )
    if token:
        api_caller(
            "POST",
            "/me/preferences",
            body={"com_dossier_racc_mail": True},
            headers={"Authorization": token},
        )

    if connection_request.ws79_needed:
        # Send to STR by mail
        _send_to_str(connection_request, headers)
    else:
        # Send to SAP by WS35
        _send_to_sap(connection_request, headers)

    return {"statusCode": 204}


def _send_to_sap(connection_request: Connection, headers: dict):
    response_async_demande_travaux = api_caller(
        "post",
        "/asynchrone",
        headers=headers,
        body={"Method": "post", "Path": "/demande_travaux/demande", "Type": "external", "Data": json.dumps(connection_request.format_for_sap_ws35())},
    )
    process_id = response_async_demande_travaux["ProcessId"]

    with futures.ThreadPoolExecutor() as executor:

        def upload_file(filename: str, url: str, dossier: str):
            response_fichier_upload = api_caller(
                "get",
                "/fichiers/upload",
                headers=headers,
                params={
                    "DocumentName": filename,
                    "IdDossier": dossier,
                },
            )

            put_url = response_fichier_upload.get("PutUrl", None)

            file_content = download_file_in_memory(url)
            upload_file_in_memory(put_url, file_content)

        # Upload meter photo if any
        for meter in connection_request.meters:
            if meter.photo:
                executor.submit(
                    upload_file,
                    filename=f"photo_compteur_{meter.number}_{meter.ean}.{get_file_extension(meter.photo.url)}",
                    url=meter.photo.url,
                    dossier=f"{process_id}_{meter.energy_type.activity_sector}",
                )

        # Upload request documents if any, duplicated for gas and elec if multiple activity_sector
        activity_sectors = {meter.energy_type.activity_sector for meter in connection_request.meters}
        for document in connection_request.documents:
            for activity_sector in activity_sectors:
                executor.submit(
                    upload_file,
                    filename=document.name,
                    url=document.url,
                    dossier=f"{process_id}_{activity_sector}",
                )
    for meter in connection_request.meters:
        # Only proceed if the work type is "MODI_SMART"
        if meter.work_type == WorkType.MODI_SMART:
            # Prepare the structured mail data using the attributes from `Connection` and `Meter`
            mail_data = {
                "num_ean": meter.ean,
                "num_cpt": meter.number,
                "surname": connection_request.applicant.name,
                "name": connection_request.applicant.firstname,
                "email": connection_request.applicant.email,
                "phone": connection_request.applicant.phone,
                "adresse": f"{connection_request.address.street} {connection_request.address.number} {connection_request.address.postcode} {connection_request.address.city}",
                "act_as": connection_request.act_as.value,
                "contact_enterprise_surname": connection_request.contact.name if connection_request.contact else None,
                "contact_enterprise_name": connection_request.contact.firstname if connection_request.contact else None,
                "contact_enterprise_mail": connection_request.contact.email if connection_request.contact else None,
                "contact_enterprise_phone": connection_request.contact.phone if connection_request.contact else None,
                "enterprise_name": connection_request.company.name if connection_request.company else None,
                "enterprise_legal_status": connection_request.company.legal_status if connection_request.company else None,
                "enterprise_vat_num": connection_request.company.vat if connection_request.company else None,
                "enterprise_adresse": (
                    f"{connection_request.contact.address.street} "
                    f"{connection_request.contact.address.number} "
                    f"{connection_request.contact.address.postcode} "
                    f"{connection_request.contact.address.city}"
                )
                if connection_request.contact
                else None,
                "enterprise_vat_subject": "Yes" if connection_request.subject_to_vat else "No",
                "is_picture": "Yes" if meter.photo else "No",
            }
            # Process `mail_data` as needed

            api_caller(
                method="post",
                path="/envMessage",
                body={
                    "Langue": os.environ["LANG"],
                    "Header": {"TEMPLATE_ID": "REQUEST_RACC_DIGITAL", "EMAIL": (connection_request.contact or connection_request.applicant).email, "NO_USER_CHECK": "Y"},
                    "Content": mail_data,
                },
            )


def _send_to_str(connection_request: Connection, headers: dict):
    api_caller(
        "post",
        "/formulaire/demande",
        headers=headers,
        body=connection_request.format_for_str_ws79(),
    )
